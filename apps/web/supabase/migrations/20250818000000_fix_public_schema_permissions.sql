-- Fix public schema permissions for task status functions
-- This migration ensures that authenticated users can execute functions in the public schema

-- Grant usage on the public schema to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;

-- Grant execute permissions on all task status functions to authenticated users
GRANT EXECUTE ON FUNCTION public.get_company_task_statuses(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_company_task_status(UUID, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_company_task_status(UUID, TEXT, INTEGER, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.reorder_company_task_statuses(UUID, UUID[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_company_task_status(UUID, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_valid_task_status(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.initialize_company_task_statuses(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.init_task_statuses_for_new_company() TO authenticated;

-- Ensure the company_task_statuses table is accessible
GRANT SELECT, INSERT, UPDATE, DELETE ON public.company_task_statuses TO authenticated;

-- Grant usage on sequences if they exist
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
