-- Fix anon role permissions for public schema
-- This migration ensures that the anon role can access the public schema and execute functions

-- Grant usage on the public schema to anon role
GRANT USAGE ON SCHEMA public TO anon;

-- Grant execute permissions on task status functions to anon role
GRANT EXECUTE ON FUNCTION public.get_company_task_statuses(UUID) TO anon;
GRANT EXECUTE ON FUNCTION public.add_company_task_status(UUID, TEXT, TEXT, TEXT, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION public.update_company_task_status(UUID, TEXT, INTEGER, TEXT, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION public.reorder_company_task_statuses(UUI<PERSON>, UUID[]) TO anon;
GRANT EXECUTE ON FUNCTION public.delete_company_task_status(UUID, UUID, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION public.is_valid_task_status(UUID, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION public.initialize_company_task_statuses(UUID) TO anon;
GRANT EXECUTE ON FUNCTION public.init_task_statuses_for_new_company() TO anon;

-- Grant table access to anon role
GRANT SELECT, INSERT, UPDATE, DELETE ON public.company_task_statuses TO anon;

-- Grant usage on sequences to anon role
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Also ensure authenticated role has all necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
