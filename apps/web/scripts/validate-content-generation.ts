#!/usr/bin/env tsx

/**
 * Validation script for the automatic content generation system
 * This script checks that all components are properly configured and working
 */

import { promises as fs } from 'fs';
import path from 'path';

interface ValidationResult {
  component: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
}

class ContentGenerationValidator {
  private results: ValidationResult[] = [];

  private addResult(component: string, status: 'pass' | 'fail' | 'warning', message: string) {
    this.results.push({ component, status, message });
  }

  async validateFileExists(filePath: string, description: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      this.addResult(description, 'pass', `File exists: ${filePath}`);
      return true;
    } catch {
      this.addResult(description, 'fail', `File missing: ${filePath}`);
      return false;
    }
  }

  async validateFileContent(filePath: string, requiredContent: string[], description: string): Promise<boolean> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const missingContent = requiredContent.filter(required => !content.includes(required));
      
      if (missingContent.length === 0) {
        this.addResult(description, 'pass', `All required content found in ${filePath}`);
        return true;
      } else {
        this.addResult(description, 'fail', `Missing content in ${filePath}: ${missingContent.join(', ')}`);
        return false;
      }
    } catch {
      this.addResult(description, 'fail', `Could not read file: ${filePath}`);
      return false;
    }
  }

  async validateEnvironmentVariables(): Promise<void> {
    const requiredEnvVars = [
      'OPENROUTER_API_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NEXT_PUBLIC_SITE_URL'
    ];

    const optionalEnvVars = [
      'AI_API_TOKEN',
      'LANGFUSE_SECRET_KEY',
      'LANGFUSE_PUBLIC_KEY'
    ];

    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        this.addResult('Environment Variables', 'pass', `${envVar} is set`);
      } else {
        this.addResult('Environment Variables', 'fail', `${envVar} is missing`);
      }
    }

    for (const envVar of optionalEnvVars) {
      if (process.env[envVar]) {
        this.addResult('Environment Variables', 'pass', `${envVar} is set (optional)`);
      } else {
        this.addResult('Environment Variables', 'warning', `${envVar} is not set (optional)`);
      }
    }
  }

  async validateContentGenerationHook(): Promise<void> {
    const hookPath = 'apps/web/app/hooks/use-content-generation.ts';
    const exists = await this.validateFileExists(hookPath, 'Content Generation Hook');
    
    if (exists) {
      await this.validateFileContent(hookPath, [
        'useContentGeneration',
        'generateContent',
        'generateBulkContent',
        'ContentGenerationOptions',
        'useZero',
        'buildPrompt'
      ], 'Content Generation Hook Content');
    }
  }

  async validateContentGenerationService(): Promise<void> {
    const servicePath = 'apps/web/app/services/content-generation.ts';
    const exists = await this.validateFileExists(servicePath, 'Content Generation Service');
    
    if (exists) {
      await this.validateFileContent(servicePath, [
        'ContentGenerationService',
        'generateTaskContent',
        'processBulkCampaignGeneration',
        'OpenAI',
        'buildPrompt',
        'convertToBlockNoteBlocks'
      ], 'Content Generation Service Content');
    }
  }

  async validateBulkGenerationAPI(): Promise<void> {
    const apiPath = 'apps/web/app/api/content-generation/trigger/route.ts';
    const exists = await this.validateFileExists(apiPath, 'Bulk Generation API');
    
    if (exists) {
      await this.validateFileContent(apiPath, [
        'POST',
        'GET',
        'bulk_campaign_generation',
        'ContentGenerationService',
        'authorization',
        'SUPABASE_SERVICE_ROLE_KEY'
      ], 'Bulk Generation API Content');
    }
  }

  async validateCampaignCreationFlow(): Promise<void> {
    const campaignPath = 'src/lib/asyncTasks/company-campaigns-insert.ts';
    const exists = await this.validateFileExists(campaignPath, 'Campaign Creation Flow');
    
    if (exists) {
      await this.validateFileContent(campaignPath, [
        'content_generation_status',
        'pending',
        'taskIds',
        '/api/content-generation/trigger',
        'bulk_campaign_generation'
      ], 'Campaign Creation Flow Content');
    }
  }

  async validateTextContentEditor(): Promise<void> {
    const editorPath = 'apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/text/TextContentEditor.tsx';
    const exists = await this.validateFileExists(editorPath, 'Text Content Editor');
    
    if (exists) {
      await this.validateFileContent(editorPath, [
        'useContentGeneration',
        'handleGenerateContent',
        'generateContentHook',
        'ContentGenerationOptions'
      ], 'Text Content Editor Content');
    }
  }

  async validateDatabaseSchema(): Promise<void> {
    const migrationPath = 'apps/web/supabase/migrations/20250813120000_add_bulk_generation_and_content_generation_columns.sql';
    const exists = await this.validateFileExists(migrationPath, 'Database Migration');
    
    if (exists) {
      await this.validateFileContent(migrationPath, [
        'content_generation_status',
        'content_generation_error',
        'content_generation_attempts',
        'auto_generated',
        'bulk_generation_status',
        'bulk_generation_progress'
      ], 'Database Migration Content');
    }

    const schemaPath = 'apps/web/app/types/schema.ts';
    const schemaExists = await this.validateFileExists(schemaPath, 'Zero Sync Schema');
    
    if (schemaExists) {
      await this.validateFileContent(schemaPath, [
        'content_generation_status',
        'bulk_generation_status',
        'enumeration',
        'pending',
        'generating',
        'completed',
        'failed'
      ], 'Zero Sync Schema Content');
    }
  }

  async runAllValidations(): Promise<void> {
    console.log('🔍 Validating Automatic Content Generation System...\n');

    await this.validateEnvironmentVariables();
    await this.validateContentGenerationHook();
    await this.validateContentGenerationService();
    await this.validateBulkGenerationAPI();
    await this.validateCampaignCreationFlow();
    await this.validateTextContentEditor();
    await this.validateDatabaseSchema();
  }

  printResults(): void {
    console.log('\n📊 Validation Results:\n');

    const groupedResults = this.results.reduce((acc, result) => {
      if (!acc[result.component]) {
        acc[result.component] = [];
      }
      acc[result.component].push(result);
      return acc;
    }, {} as Record<string, ValidationResult[]>);

    for (const [component, results] of Object.entries(groupedResults)) {
      console.log(`\n🔧 ${component}:`);
      
      for (const result of results) {
        const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
        console.log(`  ${icon} ${result.message}`);
      }
    }

    const summary = this.results.reduce((acc, result) => {
      acc[result.status]++;
      return acc;
    }, { pass: 0, fail: 0, warning: 0 });

    console.log('\n📈 Summary:');
    console.log(`  ✅ Passed: ${summary.pass}`);
    console.log(`  ❌ Failed: ${summary.fail}`);
    console.log(`  ⚠️  Warnings: ${summary.warning}`);

    if (summary.fail > 0) {
      console.log('\n❌ Validation failed. Please fix the issues above before proceeding.');
      process.exit(1);
    } else if (summary.warning > 0) {
      console.log('\n⚠️  Validation passed with warnings. Review the warnings above.');
    } else {
      console.log('\n✅ All validations passed! The automatic content generation system is ready.');
    }
  }
}

// Run validation if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new ContentGenerationValidator();
  
  validator.runAllValidations()
    .then(() => validator.printResults())
    .catch((error) => {
      console.error('❌ Validation script failed:', error);
      process.exit(1);
    });
}

export { ContentGenerationValidator };
