import { SupabaseClient } from '@supabase/supabase-js';

// Batch database operations for improved performance
export interface BatchUpdateOperation {
  id: string;
  values: Record<string, any>;
}

export interface BatchInsertOperation {
  values: Record<string, any>;
}

export interface BatchOperationResult {
  success: boolean;
  processed: number;
  errors: string[];
  processingTime: number;
}

// Optimized batch update utility
export class BatchDatabaseOperations {
  private supabase: SupabaseClient;
  private batchSize: number;

  constructor(supabase: SupabaseClient, batchSize: number = 10) {
    this.supabase = supabase;
    this.batchSize = batchSize;
  }

  // Batch update multiple records efficiently
  async batchUpdate(
    tableName: string,
    operations: BatchUpdateOperation[]
  ): Promise<BatchOperationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let processed = 0;

    try {
      // Process operations in batches to avoid overwhelming the database
      for (let i = 0; i < operations.length; i += this.batchSize) {
        const batch = operations.slice(i, i + this.batchSize);
        
        // Create batch update promises
        const batchPromises = batch.map(async (operation) => {
          try {
            const { error } = await this.supabase
              .from(tableName)
              .update(operation.values)
              .eq('id', operation.id);

            if (error) {
              throw new Error(`Update failed for ID ${operation.id}: ${error.message}`);
            }
            
            return { success: true, id: operation.id };
          } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error';
            errors.push(`ID ${operation.id}: ${errorMessage}`);
            return { success: false, id: operation.id };
          }
        });

        // Execute batch and wait for completion
        const batchResults = await Promise.allSettled(batchPromises);
        processed += batchResults.filter(result => 
          result.status === 'fulfilled' && result.value.success
        ).length;
      }

      const processingTime = Date.now() - startTime;
      
      return {
        success: errors.length === 0,
        processed,
        errors,
        processingTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Batch operation failed: ${errorMessage}`);
      
      return {
        success: false,
        processed,
        errors,
        processingTime: Date.now() - startTime
      };
    }
  }

  // Batch insert multiple records efficiently
  async batchInsert(
    tableName: string,
    operations: BatchInsertOperation[]
  ): Promise<BatchOperationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let processed = 0;

    try {
      // Process operations in batches
      for (let i = 0; i < operations.length; i += this.batchSize) {
        const batch = operations.slice(i, i + this.batchSize);
        const values = batch.map(op => op.values);

        try {
          const { error, count } = await this.supabase
            .from(tableName)
            .insert(values);

          if (error) {
            throw new Error(`Batch insert failed: ${error.message}`);
          }

          processed += count || batch.length;
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Unknown error';
          errors.push(`Batch ${i / this.batchSize + 1}: ${errorMessage}`);
        }
      }

      const processingTime = Date.now() - startTime;
      
      return {
        success: errors.length === 0,
        processed,
        errors,
        processingTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Batch operation failed: ${errorMessage}`);
      
      return {
        success: false,
        processed,
        errors,
        processingTime: Date.now() - startTime
      };
    }
  }

  // Optimized bulk status update for content generation
  async updateContentGenerationStatuses(
    updates: Array<{
      id: string;
      status: 'pending' | 'generating' | 'completed' | 'failed';
      content?: string;
      contentBlocks?: any;
      error?: string;
    }>
  ): Promise<BatchOperationResult> {
    const operations: BatchUpdateOperation[] = updates.map(update => ({
      id: update.id,
      values: {
        content_generation_status: update.status,
        is_generating: update.status === 'generating',
        ...(update.content && { content: update.content }),
        ...(update.contentBlocks && { content_editor_template: update.contentBlocks }),
        ...(update.error && { generation_error: update.error }),
        updated_at: new Date().toISOString()
      }
    }));

    return this.batchUpdate('company_content', operations);
  }

  // Optimized campaign progress update
  async updateCampaignProgress(
    campaignId: string,
    progress: {
      status?: 'pending' | 'in_progress' | 'completed' | 'failed';
      progress?: number;
      completedCount?: number;
      error?: string;
    }
  ): Promise<void> {
    const updateValues: Record<string, any> = {
      updated_at: new Date().toISOString()
    };

    if (progress.status) {
      updateValues.bulk_generation_status = progress.status;
      
      if (progress.status === 'in_progress' && !updateValues.bulk_generation_started_at) {
        updateValues.bulk_generation_started_at = Date.now();
      }
      
      if (progress.status === 'completed') {
        updateValues.bulk_generation_completed_at = Date.now();
      }
    }

    if (progress.progress !== undefined) {
      updateValues.bulk_generation_progress = progress.progress;
    }

    if (progress.completedCount !== undefined) {
      updateValues.tasks_content_generated_count = progress.completedCount;
    }

    if (progress.error) {
      updateValues.bulk_generation_error = progress.error;
    }

    await this.supabase
      .from('company_campaigns')
      .update(updateValues)
      .eq('id', campaignId);
  }
}

// Utility function to create batch operations
export function createBatchDatabaseOperations(supabase: SupabaseClient, batchSize?: number): BatchDatabaseOperations {
  return new BatchDatabaseOperations(supabase, batchSize);
}

// Connection pool management for better performance
export class DatabaseConnectionPool {
  private static instance: DatabaseConnectionPool;
  private connections: Map<string, SupabaseClient> = new Map();
  private maxConnections: number = 10;

  private constructor() {}

  static getInstance(): DatabaseConnectionPool {
    if (!DatabaseConnectionPool.instance) {
      DatabaseConnectionPool.instance = new DatabaseConnectionPool();
    }
    return DatabaseConnectionPool.instance;
  }

  getConnection(key: string, supabase: SupabaseClient): SupabaseClient {
    if (!this.connections.has(key) && this.connections.size < this.maxConnections) {
      this.connections.set(key, supabase);
    }
    return this.connections.get(key) || supabase;
  }

  releaseConnection(key: string): void {
    this.connections.delete(key);
  }

  getActiveConnections(): number {
    return this.connections.size;
  }
}
