import { SupabaseClient } from '@supabase/supabase-js';

// Performance metrics interface
export interface PerformanceMetrics {
  timestamp: number;
  campaignId: string;
  companyId: string;
  
  // Task metrics
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  
  // Timing metrics
  processingTime: number; // milliseconds
  averageTaskTime: number; // milliseconds
  queueWaitTime?: number; // milliseconds
  
  // Throughput metrics
  throughput: number; // tasks per second
  peakThroughput: number; // highest throughput achieved
  
  // Resource metrics
  concurrency: number; // concurrent tasks processed
  retryCount: number;
  errorRate: number; // percentage
  
  // API metrics
  apiCalls: number;
  apiLatency: number; // average API response time
  apiErrors: number;
  
  // System metrics
  memoryUsage?: number; // MB
  cpuUsage?: number; // percentage
  
  // Configuration
  batchSize: number;
  maxConcurrency: number;
  
  // Status
  status: 'completed' | 'failed' | 'cancelled';
  errors?: string[];
}

export interface PerformanceAlert {
  id: string;
  timestamp: number;
  campaignId: string;
  type: 'performance' | 'error' | 'resource' | 'timeout';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metrics: Partial<PerformanceMetrics>;
  resolved: boolean;
}

export interface PerformanceThresholds {
  maxProcessingTime: number; // milliseconds
  minThroughput: number; // tasks per second
  maxErrorRate: number; // percentage
  maxRetryRate: number; // percentage
  maxApiLatency: number; // milliseconds
}

// Performance monitoring and alerting system
export class PerformanceMonitor {
  private supabase: SupabaseClient;
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private alerts: PerformanceAlert[] = [];
  private thresholds: PerformanceThresholds;
  private isMonitoring: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(supabase: SupabaseClient, thresholds?: Partial<PerformanceThresholds>) {
    this.supabase = supabase;
    this.thresholds = {
      maxProcessingTime: 300000, // 5 minutes
      minThroughput: 0.5, // 0.5 tasks per second
      maxErrorRate: 10, // 10%
      maxRetryRate: 20, // 20%
      maxApiLatency: 5000, // 5 seconds
      ...thresholds
    };
  }

  // Start performance monitoring
  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.checkPerformanceThresholds();
      this.cleanupOldMetrics();
    }, intervalMs);

    console.log('Performance monitoring started');
  }

  // Stop performance monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('Performance monitoring stopped');
  }

  // Record performance metrics for a campaign
  recordMetrics(metrics: PerformanceMetrics): void {
    this.metrics.set(metrics.campaignId, metrics);
    
    // Check for immediate alerts
    this.checkMetricsForAlerts(metrics);
    
    // Log performance summary
    console.log(`Performance metrics recorded for campaign ${metrics.campaignId}:`, {
      throughput: `${metrics.throughput.toFixed(2)} tasks/sec`,
      processingTime: `${metrics.processingTime}ms`,
      errorRate: `${metrics.errorRate.toFixed(1)}%`,
      status: metrics.status
    });
  }

  // Check metrics against thresholds and generate alerts
  private checkMetricsForAlerts(metrics: PerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];

    // Processing time alert
    if (metrics.processingTime > this.thresholds.maxProcessingTime) {
      alerts.push({
        id: `perf_${metrics.campaignId}_${Date.now()}`,
        timestamp: Date.now(),
        campaignId: metrics.campaignId,
        type: 'performance',
        severity: 'high',
        message: `Processing time exceeded threshold: ${metrics.processingTime}ms > ${this.thresholds.maxProcessingTime}ms`,
        metrics: { processingTime: metrics.processingTime },
        resolved: false
      });
    }

    // Throughput alert
    if (metrics.throughput < this.thresholds.minThroughput) {
      alerts.push({
        id: `throughput_${metrics.campaignId}_${Date.now()}`,
        timestamp: Date.now(),
        campaignId: metrics.campaignId,
        type: 'performance',
        severity: 'medium',
        message: `Throughput below threshold: ${metrics.throughput.toFixed(2)} < ${this.thresholds.minThroughput} tasks/sec`,
        metrics: { throughput: metrics.throughput },
        resolved: false
      });
    }

    // Error rate alert
    if (metrics.errorRate > this.thresholds.maxErrorRate) {
      alerts.push({
        id: `error_${metrics.campaignId}_${Date.now()}`,
        timestamp: Date.now(),
        campaignId: metrics.campaignId,
        type: 'error',
        severity: 'high',
        message: `Error rate exceeded threshold: ${metrics.errorRate.toFixed(1)}% > ${this.thresholds.maxErrorRate}%`,
        metrics: { errorRate: metrics.errorRate, failedTasks: metrics.failedTasks },
        resolved: false
      });
    }

    // API latency alert
    if (metrics.apiLatency > this.thresholds.maxApiLatency) {
      alerts.push({
        id: `api_${metrics.campaignId}_${Date.now()}`,
        timestamp: Date.now(),
        campaignId: metrics.campaignId,
        type: 'performance',
        severity: 'medium',
        message: `API latency exceeded threshold: ${metrics.apiLatency}ms > ${this.thresholds.maxApiLatency}ms`,
        metrics: { apiLatency: metrics.apiLatency },
        resolved: false
      });
    }

    // Add alerts to the list
    this.alerts.push(...alerts);

    // Log critical alerts
    alerts.forEach(alert => {
      if (alert.severity === 'critical' || alert.severity === 'high') {
        console.warn(`PERFORMANCE ALERT [${alert.severity.toUpperCase()}]:`, alert.message);
      }
    });
  }

  // Check all active metrics against thresholds
  private checkPerformanceThresholds(): void {
    for (const metrics of this.metrics.values()) {
      this.checkMetricsForAlerts(metrics);
    }
  }

  // Clean up old metrics (keep last 24 hours)
  private cleanupOldMetrics(): void {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    for (const [campaignId, metrics] of this.metrics.entries()) {
      if (metrics.timestamp < cutoff) {
        this.metrics.delete(campaignId);
      }
    }

    // Clean up old alerts (keep last 7 days)
    const alertCutoff = Date.now() - (7 * 24 * 60 * 60 * 1000);
    this.alerts = this.alerts.filter(alert => alert.timestamp > alertCutoff);
  }

  // Get performance summary
  getPerformanceSummary(): {
    totalCampaigns: number;
    averageThroughput: number;
    averageProcessingTime: number;
    averageErrorRate: number;
    activeAlerts: number;
    recentMetrics: PerformanceMetrics[];
  } {
    const recentMetrics = Array.from(this.metrics.values())
      .filter(m => m.timestamp > Date.now() - (60 * 60 * 1000)) // Last hour
      .sort((a, b) => b.timestamp - a.timestamp);

    const averageThroughput = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, m) => sum + m.throughput, 0) / recentMetrics.length
      : 0;

    const averageProcessingTime = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, m) => sum + m.processingTime, 0) / recentMetrics.length
      : 0;

    const averageErrorRate = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, m) => sum + m.errorRate, 0) / recentMetrics.length
      : 0;

    const activeAlerts = this.alerts.filter(a => !a.resolved).length;

    return {
      totalCampaigns: this.metrics.size,
      averageThroughput,
      averageProcessingTime,
      averageErrorRate,
      activeAlerts,
      recentMetrics: recentMetrics.slice(0, 10) // Last 10 campaigns
    };
  }

  // Get alerts
  getAlerts(unresolved: boolean = false): PerformanceAlert[] {
    return unresolved 
      ? this.alerts.filter(a => !a.resolved)
      : [...this.alerts];
  }

  // Resolve an alert
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      return true;
    }
    return false;
  }

  // Get metrics for a specific campaign
  getCampaignMetrics(campaignId: string): PerformanceMetrics | null {
    return this.metrics.get(campaignId) || null;
  }

  // Export metrics for analysis
  exportMetrics(): {
    metrics: PerformanceMetrics[];
    alerts: PerformanceAlert[];
    summary: ReturnType<typeof this.getPerformanceSummary>;
  } {
    return {
      metrics: Array.from(this.metrics.values()),
      alerts: this.alerts,
      summary: this.getPerformanceSummary()
    };
  }

  // Update thresholds
  updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    console.log('Performance thresholds updated:', this.thresholds);
  }
}

// Singleton instance
let performanceMonitorInstance: PerformanceMonitor | null = null;

export function getPerformanceMonitor(supabase: SupabaseClient): PerformanceMonitor {
  if (!performanceMonitorInstance) {
    performanceMonitorInstance = new PerformanceMonitor(supabase);
    performanceMonitorInstance.startMonitoring();
  }
  return performanceMonitorInstance;
}

// Utility function to calculate performance metrics
export function calculatePerformanceMetrics(
  campaignId: string,
  companyId: string,
  startTime: number,
  endTime: number,
  totalTasks: number,
  completedTasks: number,
  failedTasks: number,
  retryCount: number,
  apiCalls: number,
  apiLatency: number,
  apiErrors: number,
  config: { batchSize: number; maxConcurrency: number }
): PerformanceMetrics {
  const processingTime = endTime - startTime;
  const averageTaskTime = totalTasks > 0 ? processingTime / totalTasks : 0;
  const throughput = processingTime > 0 ? (totalTasks / processingTime) * 1000 : 0;
  const errorRate = totalTasks > 0 ? (failedTasks / totalTasks) * 100 : 0;

  return {
    timestamp: endTime,
    campaignId,
    companyId,
    totalTasks,
    completedTasks,
    failedTasks,
    processingTime,
    averageTaskTime,
    throughput,
    peakThroughput: throughput, // For now, same as throughput
    concurrency: config.maxConcurrency,
    retryCount,
    errorRate,
    apiCalls,
    apiLatency,
    apiErrors,
    batchSize: config.batchSize,
    maxConcurrency: config.maxConcurrency,
    status: failedTasks === 0 ? 'completed' : 'completed' // Completed with some failures
  };
}
