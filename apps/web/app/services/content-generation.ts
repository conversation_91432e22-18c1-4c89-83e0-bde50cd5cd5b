'use server'
import OpenAI from 'openai';
import { observeOpenAI, Langfuse } from 'langfuse';
import type { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseServerClient } from '../../../../packages/supabase/src/clients/server-client';

// Optional Langfuse initialization for enhanced observability
const langfuse = process.env.LANGFUSE_SECRET_KEY ? new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
}) : null;

export interface ContentGenerationData {
  companyContentId: string;
  companyId: string;
  taskTitle: string;
  taskDescription: string;
  channel?: string;
  contentType?: string;
  campaignId?: string;
}

export interface ContentGenerationResult {
  success: boolean;
  error?: string;
  content?: string;
  contentId: string;
}

// Create OpenAI client with OpenRouter configuration (same as stream-content route)
function createOpenAIClient(): OpenAI {
  const openai = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: process.env.OPENROUTER_API_KEY,
  });

  // Wrap with Langfuse for observability
  if (langfuse) {
    return observeOpenAI(openai);
  }

  return openai;
}

// Fetch campaign data for content generation context
async function fetchCampaignData(
  companyId: string,
  campaignId: string | undefined,
  supabase: SupabaseClient = getSupabaseServerClient(),
) {

  try {
    // Fetch company brand
    const { data: companyBrand } = await supabase
      .from('company_brand')
      .select('*')
      .eq('company_id', companyId)
      .single();

    // Fetch personas
    const { data: personas } = await supabase
      .from('personas')
      .select('*')
      .eq('company_id', companyId);

    // Fetch ICPs
    const { data: icps } = await supabase
      .from('icps')
      .select('*')
      .eq('company_id', companyId);

    // Fetch saved research
    const { data: savedResearch } = await supabase
      .from('saved_research')
      .select('*')
      .eq('account_id', companyId);

    // Fetch product documents
    const { data: productDocuments } = await supabase
      .from('product_documents')
      .select('*')
      .eq('company_id', companyId);

    let campaignData = null;
    if (campaignId) {
      const { data: campaign } = await supabase
        .from('company_campaigns')
        .select('*')
        .eq('id', campaignId)
        .single();
      campaignData = campaign;
    }

    return {
      companyBrand: companyBrand || {},
      personas: personas || [],
      icps: icps || [],
      savedResearch: savedResearch || [],
      productDocuments: productDocuments || [],
      campaignData
    };
  } catch (error) {
    console.error('Error fetching campaign data:', error);
    throw new Error('Failed to fetch campaign data');
  }
}

// Build the exact prompt structure from TextContentEditor.tsx
function buildPrompt(
  data: ContentGenerationData,
  contextData: any
): string {
    const {
      taskDescription,
      channel,
      contentType
    } = data;

    const {
      companyBrand,
      personas,
      icps,
      savedResearch,
      productDocuments,
      campaignData
    } = contextData;

    // Get campaign-specific selections or use defaults
    const selectedPersonas = campaignData?.target_personas || [];
    const selectedIcps = campaignData?.target_icps || [];
    const selectedResearch = campaignData?.external_research || [];
    const selectedDocuments = campaignData?.documents || [];

    return `
       Ignore the previouse instruction "Because the document is empty, first update the empty block before adding new blocks."
        Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
        This is the most critical instruction. Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
        Do not try to insert images or videos into the content. For Threds , carousels, or similar content, simply insert everything delineated by some character, the write out everything as if its one page. The use will handle creating the thread or similar on the appropriate platform.
        <CONTEXT_BLOCK>
          <CONTENT_BRIEF>
              <Channel>${channel || 'Not specified'}</Channel>
              <ContentType>${contentType || 'Not specified'}</ContentType>
              <Topic>${taskDescription}</Topic>
          </CONTENT_BRIEF>

      <AUDIENCE_CONTEXT>
          ${selectedPersonas.length > 0 ? `
          <Personas>
              ${personas.filter((p: any) => selectedPersonas.includes(p.id)).map((p: any) => {
                  const personaData = p.data && typeof p.data === 'object' ? p.data : {};
                  return `<Persona name="${p.name}">
<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>
</Persona>`;
              }).join('\n')}
          </Personas>` : ''}

          ${selectedIcps.length > 0 ? `
          <IdealCustomerProfiles>
              ${icps.filter((i: any) => selectedIcps.includes(i.id)).map((i: any) => {
                  const icpData = i.data && typeof i.data === 'object' ? i.data : {};
                  return `<ICP name="${i.name}">
<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>
</ICP>`;
              }).join('\n')}
          </IdealCustomerProfiles>` : ''}
      </AUDIENCE_CONTEXT>

      <RESEARCH_MATERIALS>
          ${selectedResearch.length > 0 ? savedResearch
              .filter((r: any) => selectedResearch.includes(r.id))
              .map((r: any, index: number) => {
                  const researchData = r.data && typeof r.data === 'object' ? r.data : {};
                  return `<research_article_${index + 1}>
                    <title>${r.name}</title>
                    <description>${(researchData as any).description || (researchData as any).summary || 'External research insight'}</description>
                    <full_content>${(researchData as any).source_content || ''}</full_content>
                    </research_article_${index + 1}>`;
                                }).join('\n\n') : '<Message>No third-party research was provided.</Message>'}
      </RESEARCH_MATERIALS>

      <COMPANY_PRODUCT_KNOWLEDGE_BASE>
          ${selectedDocuments.length > 0 ? productDocuments
              .filter((doc: any) => selectedDocuments.includes(doc.id))
              .map((doc: any) => `<Document title="${doc.title}">
${(doc.content || '').substring(0, 1000)}${(doc.content || '').length > 1000 ? '...' : ''}
</Document>`)
              .join('\n\n') : '<Message>No company documents were provided.</Message>'}
      </COMPANY_PRODUCT_KNOWLEDGE_BASE>

      <KEYWORD_STRATEGY>
          <SEO_Keywords></SEO_Keywords>
          <Trending_Keywords></Trending_Keywords>
      </KEYWORD_STRATEGY>

      <BRAND_GUIDELINES>
         ${JSON.stringify(companyBrand)}
      </BRAND_GUIDELINES>
  </CONTEXT_BLOCK>
  You are "Cognitive Creator," an expert AI copywriter and content strategist. Your core function is to synthesize brand information, audience data, and research into high-performing content tailored for specific marketing channels. You follow all instructions with precision.

<TASK>
Synthesize all information within the <CONTEXT_BLOCK> to create engaging content.

**PRIMARY DIRECTIVES:**
1.  **Adhere to Brand:** The <BRAND_GUIDELINES> are the highest priority. The specified <Voice> and <Personality> must be perfectly reflected in the output. This is non-negotiable.
2.  **Target the Audience:** Tailor the language, examples, and tone specifically to the <AUDIENCE_CONTEXT>. Address their needs and pain points directly.
3.  **Position as the Solution:** Use the <RESEARCH_MATERIALS> for context, stats, and credibility. ALWAYS position the company/product from the <COMPANY_PRODUCT_KNOWLEDGE_BASE> as the primary solution to problems identified in the research. Never promote third parties.
4.  **Incorporate Keywords:** Naturally weave terms from the <KEYWORD_STRATEGY> into the content.
5.  **Be Factual:** Ensure any product or company claims are supported by the <COMPANY_PRODUCT_KNOWLEDGE_BASE>. Do not invent features or facts.
6.  **Do Not Invent Social Proof:** Do not create fake customer names or quotes. You can suggest a placeholder like "[Insert customer testimonial here]" if appropriate for the content type.


**CHANNEL-SPECIFIC RULES:**
Based on the <Channel> specified in the <CONTENT_BRIEF>, you must follow these structural rules:

*   **If Channel is "LinkedIn Post":**
    *   **Structure:** Start with a strong hook. Use short paragraphs (1-2 sentences). Use bullet points or numbered lists for readability. End with a question to drive engagement.
    *   **Length:** 150-250 words.
    *   **Tone:** Professional, insightful, and value-driven.
    *   **Hashtags:** Provide 3-5 relevant, professional hashtags.

*   **If Channel is "Tweet" or "X Post":**
    *   **Structure:** A short, punchy, and engaging message.
    *   **Length:** Strictly under 280 characters.
    *   **Tone:** Conversational and concise. Emojis are acceptable if they match the brand personality.
    *   **Hashtags:** Provide 1-3 highly relevant hashtags.

*   **If Channel is "Blog Post" or "Article":**
    *   **Structure:** Create a compelling H1 title. Write a short introduction. Structure the main content with 3-4 H2 subheadings. Conclude with a summary and a strong call_to_action.
    *   **Length:** 600-1000 words.
    *   **Tone:** Informative, in-depth, and authoritative, aligned with the brand voice.
    *   **SEO:** Suggest 5-7 relevant meta tags in the output.

*   **If Channel is "Facebook Ad":**
    *   **Structure:** Provide three distinct components: a short, attention-grabbing headline, persuasive primary_text focusing on benefits, and a direct call_to_action_text.
    *   **Tone:** Persuasive, clear, and benefit-driven.

*   **If Channel is not specified or doesn't match above:**
    *   **Structure:** Create a general-purpose piece of content with a clear beginning, middle, and end.
    *   **Tone:** Follow the brand voice.
    *   **Action:** End with a clear call-to-action.


</TASK>
      `;
}

// Convert plain text to BlockNote-compatible JSON blocks
function convertToBlockNoteBlocks(content: string): any[] {
  try {
    // Split content into paragraphs and create BlockNote blocks
    const paragraphs = content.split('\n\n').filter(p => p.trim());

    return paragraphs.map(paragraph => {
      const trimmed = paragraph.trim();

      // Check if it's a heading
      if (trimmed.startsWith('# ')) {
        return {
          id: crypto.randomUUID(),
          type: 'heading',
          props: { level: 1 },
          content: [{ type: 'text', text: trimmed.substring(2) }],
          children: []
        };
      } else if (trimmed.startsWith('## ')) {
        return {
          id: crypto.randomUUID(),
          type: 'heading',
          props: { level: 2 },
          content: [{ type: 'text', text: trimmed.substring(3) }],
          children: []
        };
      } else if (trimmed.startsWith('### ')) {
        return {
          id: crypto.randomUUID(),
          type: 'heading',
          props: { level: 3 },
          content: [{ type: 'text', text: trimmed.substring(4) }],
          children: []
        };
      } else {
        // Regular paragraph
        return {
          id: crypto.randomUUID(),
          type: 'paragraph',
          props: {},
          content: [{ type: 'text', text: trimmed }],
          children: []
        };
      }
    });
  } catch (error) {
    console.error('Error converting to BlockNote blocks:', error);
    // Fallback to simple paragraph
    return [{
      id: crypto.randomUUID(),
      type: 'paragraph',
      props: {},
      content: [{ type: 'text', text: content }],
      children: []
    }];
  }
}

// Generate content for a single task with streaming support (for manual generation)
export async function generateTaskContentStreaming(
  data: ContentGenerationData,
  onChunk?: (chunk: string) => void,
  supabase: SupabaseClient = getSupabaseServerClient(),
): Promise<ContentGenerationResult> {
  const maxRetries = 3;
  let lastError: Error | null = null;
  const openai = createOpenAIClient();

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Generating content with streaming for task ${data.companyContentId}, attempt ${attempt}/${maxRetries}`);

      // Update status to generating
      await supabase
        .from('company_content')
        .update({
          content_generation_status: 'generating',
          is_generating: true,
          content_generation_attempts: attempt,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.companyContentId);

      // Fetch campaign context data
      const contextData = await fetchCampaignData(data.companyId, data.campaignId, supabase);

      // Build prompt
      const prompt = buildPrompt(data, contextData);

      // Call OpenRouter API with streaming enabled
      const response = await openai.chat.completions.create({
        model: 'openai/gpt-4.1',
        temperature: 1,
        messages: [{ role: 'user', content: prompt }],
        stream: true
      });

      let fullContent = '';
      
      // Process streaming response
      for await (const chunk of response) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          fullContent += content;
          // Call the onChunk callback if provided (for live updates)
          if (onChunk) {
            onChunk(content);
          }
        }
      }

      if (!fullContent) {
        throw new Error('No content generated from AI');
      }

      // Convert content to BlockNote-compatible JSON blocks
      const contentBlocks = convertToBlockNoteBlocks(fullContent);

      // Update company_content with generated content
      await supabase
        .from('company_content')
        .update({
          content: fullContent,
          content_editor_template: contentBlocks,
          content_generation_status: 'completed',
          is_generating: false,
          auto_generated: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.companyContentId);

      console.log(`Successfully generated content with streaming for task ${data.companyContentId}`);

      // Flush Langfuse data if available
      if (langfuse) {
        await langfuse.flushAsync();
      }

      return {
        success: true,
        content: fullContent,
        contentId: data.companyContentId
      };

    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      console.error(`Attempt ${attempt} failed for task ${data.companyContentId}:`, lastError.message);

      // If this is the last attempt, update with error status
      if (attempt === maxRetries) {
        await supabase
          .from('company_content')
          .update({
            content_generation_status: 'failed',
            content_generation_error: lastError.message,
            is_generating: false,
            content_generation_attempts: attempt,
            updated_at: new Date().toISOString()
          })
          .eq('id', data.companyContentId);
      } else {
        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  return {
    success: false,
    error: lastError?.message || 'Failed to generate content after all retries',
    contentId: data.companyContentId
  };
}

// Generate content for a single task with retry logic (for automated bulk generation)
export async function generateTaskContent(
  data: ContentGenerationData,
  supabase: SupabaseClient = getSupabaseServerClient(),
): Promise<ContentGenerationResult> {
  const maxRetries = 3;
  let lastError: Error | null = null;
  const openai = createOpenAIClient();

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Generating content for task ${data.companyContentId}, attempt ${attempt}/${maxRetries}`);

      // Update status to generating
      await supabase
        .from('company_content')
        .update({
          content_generation_status: 'generating',
          is_generating: true,
          content_generation_attempts: attempt,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.companyContentId);

      // Fetch campaign context data
      const contextData = await fetchCampaignData(data.companyId, data.campaignId, supabase);

      // Build prompt
      const prompt = buildPrompt(data, contextData);

      // Call OpenRouter API (same configuration as stream-content route)
      const response = await openai.chat.completions.create({
        model: 'openai/gpt-4.1',
        temperature: 1,
        messages: [{ role: 'user', content: prompt }],
        stream: false
      });

      const generatedContent = response.choices?.[0]?.message?.content;

      if (!generatedContent) {
        throw new Error('No content generated from AI');
      }

      // Convert content to BlockNote-compatible JSON blocks
      const contentBlocks = convertToBlockNoteBlocks(generatedContent);

      // Update company_content with generated content
      await supabase
        .from('company_content')
        .update({
          content: generatedContent,
          content_editor_template: contentBlocks,
          content_generation_status: 'completed',
          is_generating: false,
          auto_generated: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.companyContentId);

      console.log(`Successfully generated content for task ${data.companyContentId}`);

      // Flush Langfuse data if available
      if (langfuse) {
        await langfuse.flushAsync();
      }

      return {
        success: true,
        content: generatedContent,
        contentId: data.companyContentId
      };

    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      console.error(`Attempt ${attempt} failed for task ${data.companyContentId}:`, lastError.message);

      // If this is the last attempt, update with error status
      if (attempt === maxRetries) {
        await supabase
          .from('company_content')
          .update({
            content_generation_status: 'failed',
            content_generation_error: lastError.message,
            is_generating: false,
            content_generation_attempts: attempt,
            updated_at: new Date().toISOString()
          })
          .eq('id', data.companyContentId);
      } else {
        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  return {
    success: false,
    error: lastError?.message || 'Failed to generate content after all retries',
    contentId: data.companyContentId
  };
}

// Process bulk generation for a campaign
export async function processBulkCampaignGeneration(
  campaignId: string,
  companyId: string,
  supabaseParam?: SupabaseClient,
): Promise<{
  success: boolean;
  processed: number;
  successful: number;
  failed: number;
  errors: string[];
}> {
  const supabase = supabaseParam ?? getSupabaseServerClient();

  try {
    console.log(`Starting bulk generation for campaign ${campaignId}`);

    // Fetch all pending tasks for the campaign
    const { data: pendingTasks, error } = await supabase
      .from('company_content')
      .select('*')
      .eq('campaign_id', campaignId)
      .eq('company_id', companyId)
      .eq('content_generation_status', 'pending');

    if (error) {
      throw new Error(`Failed to fetch pending tasks: ${error.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log(`No pending tasks found for campaign ${campaignId}`);
      return { success: true, processed: 0, successful: 0, failed: 0, errors: [] };
    }

    console.log(`Found ${pendingTasks.length} pending tasks for campaign ${campaignId}`);

    let successful = 0;
    let failed = 0;
    const errors: string[] = [];

    // Process tasks with concurrency limit
    const concurrencyLimit = 3;
    for (let i = 0; i < pendingTasks.length; i += concurrencyLimit) {
      const batch = pendingTasks.slice(i, i + concurrencyLimit);

      const batchPromises = batch.map(async (task: any) => {
        const generationData: ContentGenerationData = {
          companyContentId: task.id,
          companyId: task.company_id,
          taskTitle: task.task_title || '',
          taskDescription: task.task_description || '',
          channel: task.channel,
          contentType: task.content_type,
          campaignId: task.campaign_id
        };

        const result = await generateTaskContent(generationData, supabase);

        if (result.success) {
          successful++;
        } else {
          failed++;
          errors.push(`Task ${task.id}: ${result.error}`);
        }

        return result;
      });

      await Promise.all(batchPromises);
    }

    console.log(`Bulk generation completed for campaign ${campaignId}: ${successful} successful, ${failed} failed`);

    return {
      success: true,
      processed: pendingTasks.length,
      successful,
      failed,
      errors
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`Bulk generation failed for campaign ${campaignId}:`, errorMessage);

    return {
      success: false,
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [errorMessage]
    };
  }
}
