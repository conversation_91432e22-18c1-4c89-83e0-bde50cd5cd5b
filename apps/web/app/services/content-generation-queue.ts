import { SupabaseClient } from '@supabase/supabase-js';

// Job queue system for content generation
export interface ContentGenerationJob {
  id: string;
  campaignId: string;
  companyId: string;
  taskIds: string[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  progress: number;
  metadata?: Record<string, any>;
  retryCount: number;
  maxRetries: number;
  error?: string;
}

export interface QueueConfig {
  maxConcurrentJobs: number;
  maxRetries: number;
  retryDelay: number;
  jobTimeout: number;
  priorityWeights: Record<string, number>;
}

export interface QueueStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  totalThroughput: number;
  averageProcessingTime: number;
}

// Enhanced job queue manager
export class ContentGenerationQueue {
  private jobs: Map<string, ContentGenerationJob> = new Map();
  private processingJobs: Set<string> = new Set();
  private config: QueueConfig;
  private supabase: SupabaseClient;
  private isProcessing: boolean = false;
  private stats: QueueStats = {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    totalThroughput: 0,
    averageProcessingTime: 0
  };

  constructor(supabase: SupabaseClient, config?: Partial<QueueConfig>) {
    this.supabase = supabase;
    this.config = {
      maxConcurrentJobs: 5,
      maxRetries: 3,
      retryDelay: 5000,
      jobTimeout: 300000, // 5 minutes
      priorityWeights: { urgent: 4, high: 3, normal: 2, low: 1 },
      ...config
    };
  }

  // Add a new content generation job to the queue
  async addJob(
    campaignId: string,
    companyId: string,
    taskIds: string[],
    priority: ContentGenerationJob['priority'] = 'normal',
    metadata?: Record<string, any>
  ): Promise<string> {
    const jobId = `job_${campaignId}_${Date.now()}`;
    
    const job: ContentGenerationJob = {
      id: jobId,
      campaignId,
      companyId,
      taskIds,
      priority,
      status: 'pending',
      createdAt: Date.now(),
      progress: 0,
      metadata,
      retryCount: 0,
      maxRetries: this.config.maxRetries
    };

    this.jobs.set(jobId, job);
    this.stats.pending++;

    console.log(`Added job ${jobId} to queue with ${taskIds.length} tasks (priority: ${priority})`);

    // Start processing if not already running
    if (!this.isProcessing) {
      this.startProcessing();
    }

    return jobId;
  }

  // Start the queue processing loop
  private async startProcessing(): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log('Starting content generation queue processing');

    while (this.isProcessing) {
      try {
        await this.processNextJobs();
        await new Promise(resolve => setTimeout(resolve, 1000)); // Check every second
      } catch (error) {
        console.error('Error in queue processing loop:', error);
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds on error
      }
    }
  }

  // Process the next available jobs based on priority and concurrency limits
  private async processNextJobs(): Promise<void> {
    const availableSlots = this.config.maxConcurrentJobs - this.processingJobs.size;
    if (availableSlots <= 0) return;

    // Get pending jobs sorted by priority
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'pending')
      .sort((a, b) => {
        const priorityDiff = this.config.priorityWeights[b.priority] - this.config.priorityWeights[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.createdAt - b.createdAt; // FIFO for same priority
      });

    const jobsToProcess = pendingJobs.slice(0, availableSlots);

    for (const job of jobsToProcess) {
      this.processJob(job);
    }
  }

  // Process a single job
  private async processJob(job: ContentGenerationJob): Promise<void> {
    try {
      // Mark job as processing
      job.status = 'processing';
      job.startedAt = Date.now();
      this.processingJobs.add(job.id);
      this.stats.pending--;
      this.stats.processing++;

      console.log(`Starting processing job ${job.id} for campaign ${job.campaignId}`);

      // Update campaign status
      await this.supabase
        .from('company_campaigns')
        .update({
          bulk_generation_status: 'in_progress',
          bulk_generation_started_at: job.startedAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', job.campaignId);

      // Import the enhanced processing function
      const { processBulkCampaignGeneration } = await import('./content-generation');
      
      // Process the job with enhanced configuration
      const optimizationConfig = {
        maxConcurrency: job.priority === 'urgent' ? 25 : job.priority === 'high' ? 20 : 15,
        batchSize: job.priority === 'urgent' ? 10 : job.priority === 'high' ? 8 : 5,
        retryAttempts: 3,
        retryDelay: 1000,
        progressUpdateInterval: 2000,
        dbBatchSize: 15
      };

      const result = await processBulkCampaignGeneration(
        job.campaignId,
        job.companyId,
        this.supabase,
        optimizationConfig
      );

      if (result.success) {
        // Job completed successfully
        job.status = 'completed';
        job.completedAt = Date.now();
        job.progress = 100;
        this.stats.completed++;

        // Update campaign with completion status
        await this.supabase
          .from('company_campaigns')
          .update({
            bulk_generation_status: 'completed',
            bulk_generation_completed_at: job.completedAt,
            tasks_content_generated_count: result.successful,
            bulk_generation_progress: 100,
            updated_at: new Date().toISOString()
          })
          .eq('id', job.campaignId);

        console.log(`Job ${job.id} completed successfully: ${result.successful}/${result.processed} tasks`);
        
        // Update throughput stats
        const processingTime = job.completedAt - job.startedAt!;
        this.updateStats(processingTime, result.processed);

      } else {
        throw new Error(result.errors.join('; '));
      }

    } catch (error) {
      await this.handleJobError(job, error);
    } finally {
      this.processingJobs.delete(job.id);
      this.stats.processing--;
    }
  }

  // Handle job errors and retries
  private async handleJobError(job: ContentGenerationJob, error: any): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    job.retryCount++;

    console.error(`Job ${job.id} failed (attempt ${job.retryCount}/${job.maxRetries}):`, errorMessage);

    if (job.retryCount < job.maxRetries) {
      // Schedule retry
      job.status = 'pending';
      job.error = errorMessage;
      this.stats.pending++;
      
      console.log(`Scheduling retry for job ${job.id} in ${this.config.retryDelay}ms`);
      
      setTimeout(() => {
        // Job will be picked up in the next processing cycle
      }, this.config.retryDelay);

    } else {
      // Max retries exceeded
      job.status = 'failed';
      job.error = errorMessage;
      job.completedAt = Date.now();
      this.stats.failed++;

      // Update campaign with failure status
      await this.supabase
        .from('company_campaigns')
        .update({
          bulk_generation_status: 'failed',
          bulk_generation_error: errorMessage,
          updated_at: new Date().toISOString()
        })
        .eq('id', job.campaignId);

      console.error(`Job ${job.id} failed permanently after ${job.maxRetries} attempts`);
    }
  }

  // Update queue statistics
  private updateStats(processingTime: number, tasksProcessed: number): void {
    const throughput = (tasksProcessed / processingTime) * 1000; // tasks per second
    
    // Update running averages
    const totalJobs = this.stats.completed + this.stats.failed;
    this.stats.averageProcessingTime = (
      (this.stats.averageProcessingTime * (totalJobs - 1) + processingTime) / totalJobs
    );
    
    this.stats.totalThroughput = (
      (this.stats.totalThroughput * (totalJobs - 1) + throughput) / totalJobs
    );
  }

  // Get job status
  getJob(jobId: string): ContentGenerationJob | undefined {
    return this.jobs.get(jobId);
  }

  // Get queue statistics
  getStats(): QueueStats {
    return { ...this.stats };
  }

  // Cancel a job
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job || job.status === 'completed' || job.status === 'failed') {
      return false;
    }

    job.status = 'cancelled';
    job.completedAt = Date.now();
    
    if (this.processingJobs.has(jobId)) {
      this.processingJobs.delete(jobId);
      this.stats.processing--;
    } else {
      this.stats.pending--;
    }

    console.log(`Job ${jobId} cancelled`);
    return true;
  }

  // Stop the queue processing
  stop(): void {
    this.isProcessing = false;
    console.log('Content generation queue processing stopped');
  }
}

// Singleton queue instance
let queueInstance: ContentGenerationQueue | null = null;

export function getContentGenerationQueue(supabase: SupabaseClient): ContentGenerationQueue {
  if (!queueInstance) {
    queueInstance = new ContentGenerationQueue(supabase);
  }
  return queueInstance;
}
