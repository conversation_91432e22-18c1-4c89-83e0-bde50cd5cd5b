import { SupabaseClient } from '@supabase/supabase-js';

// Real-time progress tracking for content generation
export interface ProgressUpdate {
  campaignId: string;
  jobId?: string;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  currentTask?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100
  estimatedTimeRemaining: number; // milliseconds
  throughput: number; // tasks per second
  startTime: number;
  lastUpdate: number;
  errors?: string[];
  metadata?: Record<string, any>;
}

export interface ProgressSubscription {
  campaignId: string;
  callback: (update: ProgressUpdate) => void;
  unsubscribe: () => void;
}

// Real-time progress tracker using Zero Sync Engine
export class RealTimeProgressTracker {
  private supabase: SupabaseClient;
  private progressMap: Map<string, ProgressUpdate> = new Map();
  private subscriptions: Map<string, Set<(update: ProgressUpdate) => void>> = new Map();
  private updateInterval: number = 2000; // 2 seconds
  private intervalId: NodeJS.Timeout | null = null;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
    this.startProgressUpdates();
  }

  // Initialize progress tracking for a campaign
  initializeProgress(
    campaignId: string,
    totalTasks: number,
    jobId?: string,
    metadata?: Record<string, any>
  ): ProgressUpdate {
    const progress: ProgressUpdate = {
      campaignId,
      jobId,
      totalTasks,
      completedTasks: 0,
      failedTasks: 0,
      status: 'pending',
      progress: 0,
      estimatedTimeRemaining: 0,
      throughput: 0,
      startTime: Date.now(),
      lastUpdate: Date.now(),
      metadata
    };

    this.progressMap.set(campaignId, progress);
    this.notifySubscribers(campaignId, progress);
    
    console.log(`Initialized progress tracking for campaign ${campaignId} with ${totalTasks} tasks`);
    return progress;
  }

  // Update progress for a campaign
  updateProgress(
    campaignId: string,
    updates: Partial<ProgressUpdate>
  ): ProgressUpdate | null {
    const currentProgress = this.progressMap.get(campaignId);
    if (!currentProgress) {
      console.warn(`No progress tracking found for campaign ${campaignId}`);
      return null;
    }

    // Calculate new progress values
    const updatedProgress: ProgressUpdate = {
      ...currentProgress,
      ...updates,
      lastUpdate: Date.now()
    };

    // Recalculate derived values
    const completedTotal = updatedProgress.completedTasks + updatedProgress.failedTasks;
    updatedProgress.progress = Math.round((completedTotal / updatedProgress.totalTasks) * 100);

    // Calculate throughput and ETA
    const elapsed = updatedProgress.lastUpdate - updatedProgress.startTime;
    if (elapsed > 0 && completedTotal > 0) {
      updatedProgress.throughput = (completedTotal / elapsed) * 1000; // tasks per second
      
      const remaining = updatedProgress.totalTasks - completedTotal;
      if (updatedProgress.throughput > 0) {
        updatedProgress.estimatedTimeRemaining = (remaining / updatedProgress.throughput) * 1000;
      }
    }

    // Update status based on progress
    if (updatedProgress.progress === 100) {
      updatedProgress.status = updatedProgress.failedTasks > 0 ? 'completed' : 'completed';
    } else if (completedTotal > 0) {
      updatedProgress.status = 'in_progress';
    }

    this.progressMap.set(campaignId, updatedProgress);
    this.notifySubscribers(campaignId, updatedProgress);

    return updatedProgress;
  }

  // Mark a single task as completed
  markTaskCompleted(campaignId: string, taskId?: string): ProgressUpdate | null {
    const currentProgress = this.progressMap.get(campaignId);
    if (!currentProgress) return null;

    return this.updateProgress(campaignId, {
      completedTasks: currentProgress.completedTasks + 1,
      currentTask: taskId
    });
  }

  // Mark a single task as failed
  markTaskFailed(campaignId: string, taskId?: string, error?: string): ProgressUpdate | null {
    const currentProgress = this.progressMap.get(campaignId);
    if (!currentProgress) return null;

    const errors = currentProgress.errors || [];
    if (error) {
      errors.push(`Task ${taskId}: ${error}`);
    }

    return this.updateProgress(campaignId, {
      failedTasks: currentProgress.failedTasks + 1,
      currentTask: taskId,
      errors
    });
  }

  // Subscribe to progress updates for a campaign
  subscribe(
    campaignId: string,
    callback: (update: ProgressUpdate) => void
  ): ProgressSubscription {
    if (!this.subscriptions.has(campaignId)) {
      this.subscriptions.set(campaignId, new Set());
    }

    const callbacks = this.subscriptions.get(campaignId)!;
    callbacks.add(callback);

    // Send current progress if available
    const currentProgress = this.progressMap.get(campaignId);
    if (currentProgress) {
      callback(currentProgress);
    }

    const unsubscribe = () => {
      callbacks.delete(callback);
      if (callbacks.size === 0) {
        this.subscriptions.delete(campaignId);
      }
    };

    console.log(`Added progress subscription for campaign ${campaignId}`);

    return {
      campaignId,
      callback,
      unsubscribe
    };
  }

  // Notify all subscribers of progress updates
  private notifySubscribers(campaignId: string, progress: ProgressUpdate): void {
    const callbacks = this.subscriptions.get(campaignId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(progress);
        } catch (error) {
          console.error('Error in progress callback:', error);
        }
      });
    }
  }

  // Start periodic progress updates to database
  private startProgressUpdates(): void {
    if (this.intervalId) return;

    this.intervalId = setInterval(async () => {
      await this.syncProgressToDatabase();
    }, this.updateInterval);

    console.log('Started real-time progress updates');
  }

  // Sync progress to database for persistence
  private async syncProgressToDatabase(): Promise<void> {
    const updates: Array<{
      campaignId: string;
      progress: ProgressUpdate;
    }> = [];

    // Collect all progress updates
    for (const [campaignId, progress] of this.progressMap.entries()) {
      if (progress.lastUpdate > Date.now() - this.updateInterval * 2) {
        updates.push({ campaignId, progress });
      }
    }

    if (updates.length === 0) return;

    // Batch update campaigns in database
    try {
      const batchPromises = updates.map(({ campaignId, progress }) =>
        this.supabase
          .from('company_campaigns')
          .update({
            bulk_generation_status: progress.status,
            bulk_generation_progress: progress.progress,
            tasks_content_generated_count: progress.completedTasks,
            updated_at: new Date().toISOString()
          })
          .eq('id', campaignId)
      );

      await Promise.allSettled(batchPromises);
    } catch (error) {
      console.error('Error syncing progress to database:', error);
    }
  }

  // Get current progress for a campaign
  getProgress(campaignId: string): ProgressUpdate | null {
    return this.progressMap.get(campaignId) || null;
  }

  // Get all active progress tracking
  getAllProgress(): Map<string, ProgressUpdate> {
    return new Map(this.progressMap);
  }

  // Complete progress tracking for a campaign
  completeProgress(
    campaignId: string,
    finalStatus: 'completed' | 'failed' | 'cancelled' = 'completed'
  ): ProgressUpdate | null {
    const progress = this.progressMap.get(campaignId);
    if (!progress) return null;

    const finalProgress = this.updateProgress(campaignId, {
      status: finalStatus,
      progress: finalStatus === 'completed' ? 100 : progress.progress,
      estimatedTimeRemaining: 0
    });

    // Clean up after a delay
    setTimeout(() => {
      this.progressMap.delete(campaignId);
      this.subscriptions.delete(campaignId);
    }, 30000); // Keep for 30 seconds after completion

    return finalProgress;
  }

  // Stop progress tracking
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    console.log('Stopped real-time progress updates');
  }

  // Get progress statistics
  getStats(): {
    activeTracking: number;
    totalSubscriptions: number;
    averageThroughput: number;
  } {
    const activeTracking = this.progressMap.size;
    const totalSubscriptions = Array.from(this.subscriptions.values())
      .reduce((sum, callbacks) => sum + callbacks.size, 0);
    
    const throughputs = Array.from(this.progressMap.values())
      .map(p => p.throughput)
      .filter(t => t > 0);
    
    const averageThroughput = throughputs.length > 0
      ? throughputs.reduce((sum, t) => sum + t, 0) / throughputs.length
      : 0;

    return {
      activeTracking,
      totalSubscriptions,
      averageThroughput
    };
  }
}

// Singleton instance
let progressTrackerInstance: RealTimeProgressTracker | null = null;

export function getRealTimeProgressTracker(supabase: SupabaseClient): RealTimeProgressTracker {
  if (!progressTrackerInstance) {
    progressTrackerInstance = new RealTimeProgressTracker(supabase);
  }
  return progressTrackerInstance;
}

// Hook for React components to use progress tracking
export function useProgressTracking(campaignId: string, supabase: SupabaseClient) {
  const tracker = getRealTimeProgressTracker(supabase);
  
  return {
    subscribe: (callback: (update: ProgressUpdate) => void) => 
      tracker.subscribe(campaignId, callback),
    getProgress: () => tracker.getProgress(campaignId),
    markTaskCompleted: (taskId?: string) => tracker.markTaskCompleted(campaignId, taskId),
    markTaskFailed: (taskId?: string, error?: string) => 
      tracker.markTaskFailed(campaignId, taskId, error)
  };
}
