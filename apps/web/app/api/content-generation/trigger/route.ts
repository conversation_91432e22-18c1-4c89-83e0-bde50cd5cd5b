import { NextRequest, NextResponse } from 'next/server';
import { processBulkCampaignGeneration } from '../../../services/content-generation';
import { getSupabaseServerAdminClient } from '../../../../../../packages/supabase/src/clients/server-admin-client';
import { getSupabaseServerClient } from '../../../../../../packages/supabase/src/clients/server-client';

// CORS headers for the response
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
};

// Handle preflight OPTIONS requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

export async function POST(request: NextRequest) {
  try {
    // Check authorization
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401, headers: corsHeaders }
      );
    }

    const token = authHeader.split(' ')[1];
    if (token !== expectedToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401, headers: corsHeaders }
      );
    }

    // Check if OpenRouter API key is available
    if (!process.env.OPENROUTER_API_KEY) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500, headers: corsHeaders }
      );
    }

    // Parse the request body
    const requestBody = await request.json();
    const { action, campaign_id, company_id, priority } = requestBody;

    if (action !== 'bulk_campaign_generation') {
      return NextResponse.json(
        { error: 'Invalid action. Expected "bulk_campaign_generation"' },
        { status: 400, headers: corsHeaders }
      );
    }

    if (!campaign_id || !company_id) {
      return NextResponse.json(
        { error: 'Missing required fields: campaign_id and company_id' },
        { status: 400, headers: corsHeaders }
      );
    }

    console.log(`Received bulk generation request for campaign ${campaign_id}, company ${company_id}`);

    // Use admin client since we validated the service role key in the Authorization header
    const supabase = getSupabaseServerAdminClient();

    // Update campaign bulk generation status to 'in_progress'
    await supabase
      .from('company_campaigns')
      .update({
        bulk_generation_status: 'in_progress',
        bulk_generation_started_at: Date.now(),
        bulk_generation_error: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', campaign_id);

    try {
      // Process bulk generation
      const result = await processBulkCampaignGeneration(campaign_id, company_id, supabase);

      if (result.success) {
        // Update campaign with success status
        await supabase
          .from('company_campaigns')
          .update({
            bulk_generation_status: 'completed',
            bulk_generation_completed_at: Date.now(),
            tasks_content_generated_count: result.successful,
            bulk_generation_progress: 100,
            updated_at: new Date().toISOString()
          })
          .eq('id', campaign_id);

        console.log(`Bulk generation completed successfully for campaign ${campaign_id}`);

        return NextResponse.json({
          success: true,
          message: 'Bulk content generation completed successfully',
          campaign_id,
          results: {
            processed: result.processed,
            successful: result.successful,
            failed: result.failed,
            errors: result.errors
          }
        }, {
          headers: corsHeaders,
        });

      } else {
        // Update campaign with failure status
        await supabase
          .from('company_campaigns')
          .update({
            bulk_generation_status: 'failed',
            bulk_generation_error: result.errors.join('; '),
            bulk_generation_completed_at: Date.now(),
            updated_at: new Date().toISOString()
          })
          .eq('id', campaign_id);

        return NextResponse.json({
          success: false,
          error: 'Bulk generation failed',
          campaign_id,
          results: result
        }, {
          status: 500,
          headers: corsHeaders,
        });
      }

    } catch (serviceError) {
      const errorMessage = serviceError instanceof Error ? serviceError.message : 'Unknown service error';
      console.error(`Content generation service error for campaign ${campaign_id}:`, errorMessage);

      // Update campaign with failure status
      await supabase
        .from('company_campaigns')
        .update({
          bulk_generation_status: 'failed',
          bulk_generation_error: errorMessage,
          bulk_generation_completed_at: Date.now(),
          updated_at: new Date().toISOString()
        })
        .eq('id', campaign_id);

      return NextResponse.json({
        success: false,
        error: 'Content generation service failed',
        details: errorMessage,
        campaign_id
      }, {
        status: 500,
        headers: corsHeaders,
      });
    }

  } catch (error) {
    console.error('Error processing bulk content generation request:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json({
      success: false,
      error: 'Failed to process bulk content generation request',
      details: errorMessage
    }, {
      status: 500,
      headers: corsHeaders,
    });
  }
}

// GET endpoint for checking generation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const campaignId = searchParams.get('campaign_id');

    if (!campaignId) {
      return NextResponse.json(
        { error: 'Missing campaign_id parameter' },
        { status: 400, headers: corsHeaders }
      );
    }

    const supabase = getSupabaseServerClient();

    try {
      // Fetch campaign generation status
      const { data: campaign, error } = await supabase
        .from('company_campaigns')
        .select('bulk_generation_status, bulk_generation_progress, bulk_generation_error, tasks_content_generated_count, bulk_generation_started_at, bulk_generation_completed_at')
        .eq('id', campaignId)
        .single();

      if (error || !campaign) {
        return NextResponse.json(
          { error: 'Campaign not found' },
          { status: 404, headers: corsHeaders }
        );
      }

      // Fetch task generation status
      const { data: tasks, error: tasksError } = await supabase
        .from('company_content')
        .select('content_generation_status')
        .eq('campaign_id', campaignId);

      if (tasksError) {
        console.error('Error fetching task status:', tasksError);
      }

      const taskStats = tasks?.reduce((acc: Record<string, number>, task: any) => {
        acc[task.content_generation_status || 'unknown'] = (acc[task.content_generation_status || 'unknown'] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

    return NextResponse.json({
      campaign_id: campaignId,
      bulk_generation_status: campaign.bulk_generation_status,
      bulk_generation_progress: campaign.bulk_generation_progress || 0,
      bulk_generation_error: campaign.bulk_generation_error,
      tasks_content_generated_count: campaign.tasks_content_generated_count || 0,
      bulk_generation_started_at: campaign.bulk_generation_started_at,
      bulk_generation_completed_at: campaign.bulk_generation_completed_at,
      task_statistics: taskStats
    }, {
      headers: corsHeaders,
    });

    } catch (error) {
      console.error('Error fetching generation status:', error);
      return NextResponse.json(
        { error: 'Failed to fetch generation status' },
        { status: 500, headers: corsHeaders }
      );
    }

  } catch (error) {
    console.error('Error fetching generation status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch generation status' },
      { status: 500, headers: corsHeaders }
    );
  }
}
