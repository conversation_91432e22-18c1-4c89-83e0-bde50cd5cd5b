/**
 * Integration tests for the automatic content generation system
 * These tests validate that campaign creation triggers automatic content generation
 * and that manual generation still works correctly.
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';

// Mock environment variables for testing
const mockEnv = {
  OPENROUTER_API_KEY: 'test-openrouter-key',
  SUPABASE_SERVICE_ROLE_KEY: 'test-service-role-key',
  NEXT_PUBLIC_SITE_URL: 'http://localhost:3000',
  AI_API_TOKEN: 'my-secret-token'
};

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Content Generation Integration Tests', () => {
  beforeAll(() => {
    // Set up environment variables
    Object.assign(process.env, mockEnv);
  });

  afterAll(() => {
    // Clean up
    jest.restoreAllMocks();
  });

  describe('Bulk Generation API Route', () => {
    it('should handle bulk campaign generation request', async () => {
      const mockResponse = {
        success: true,
        message: 'Bulk content generation completed successfully',
        campaign_id: 'test-campaign-id',
        results: {
          processed: 5,
          successful: 5,
          failed: 0,
          errors: []
        }
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        text: async () => JSON.stringify(mockResponse)
      });

      const response = await fetch('/api/content-generation/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          action: 'bulk_campaign_generation',
          campaign_id: 'test-campaign-id',
          company_id: 'test-company-id',
          priority: 'high'
        })
      });

      expect(response.ok).toBe(true);
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.results.processed).toBe(5);
      expect(result.results.successful).toBe(5);
      expect(result.results.failed).toBe(0);
    });

    it('should handle authentication errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ error: 'Invalid token' })
      });

      const response = await fetch('/api/content-generation/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer invalid-token'
        },
        body: JSON.stringify({
          action: 'bulk_campaign_generation',
          campaign_id: 'test-campaign-id',
          company_id: 'test-company-id'
        })
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);
    });

    it('should handle missing required fields', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Missing required fields: campaign_id and company_id' })
      });

      const response = await fetch('/api/content-generation/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          action: 'bulk_campaign_generation'
          // Missing campaign_id and company_id
        })
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);
    });
  });

  describe('Content Generation Service', () => {
    it('should validate prompt structure', () => {
      // Test that the prompt includes all required sections
      const mockData = {
        taskDescription: 'Create a LinkedIn post about AI',
        channel: 'LinkedIn Post',
        contentType: 'Text'
      };

      const mockContextData = {
        companyBrand: { voice: 'Professional', personality: 'Expert' },
        personas: [{ id: '1', name: 'Tech Executive', data: { role: 'CTO' } }],
        icps: [{ id: '1', name: 'Enterprise', data: { size: 'Large' } }],
        savedResearch: [],
        productDocuments: [],
        campaignData: { target_personas: ['1'], target_icps: ['1'] }
      };

      // This would be the actual prompt building logic from the service
      const prompt = buildTestPrompt(mockData, mockContextData);

      expect(prompt).toContain('<CONTEXT_BLOCK>');
      expect(prompt).toContain('<CONTENT_BRIEF>');
      expect(prompt).toContain('<AUDIENCE_CONTEXT>');
      expect(prompt).toContain('<RESEARCH_MATERIALS>');
      expect(prompt).toContain('<COMPANY_PRODUCT_KNOWLEDGE_BASE>');
      expect(prompt).toContain('<KEYWORD_STRATEGY>');
      expect(prompt).toContain('<BRAND_GUIDELINES>');
      expect(prompt).toContain('LinkedIn Post');
      expect(prompt).toContain('Create a LinkedIn post about AI');
    });

    it('should handle different content types', () => {
      const contentTypes = ['LinkedIn Post', 'Tweet', 'Blog Post', 'Facebook Ad'];
      
      contentTypes.forEach(contentType => {
        const mockData = {
          taskDescription: `Create content for ${contentType}`,
          channel: contentType,
          contentType: contentType
        };

        const mockContextData = {
          companyBrand: {},
          personas: [],
          icps: [],
          savedResearch: [],
          productDocuments: [],
          campaignData: {}
        };

        const prompt = buildTestPrompt(mockData, mockContextData);
        expect(prompt).toContain(contentType);
      });
    });
  });

  describe('Campaign Creation Flow', () => {
    it('should create tasks with correct content_generation_status', () => {
      // Mock the task creation logic
      const mockTask = {
        id: 'test-task-id',
        campaign_id: 'test-campaign-id',
        company_id: 'test-company-id',
        content_type: 'Text',
        language: 'en',
        task_title: 'Test Task',
        task_description: 'Test Description',
        channel: 'LinkedIn Post',
        content_generation_status: 'pending',
        is_generating: false,
        is_draft: true
      };

      expect(mockTask.content_generation_status).toBe('pending');
      expect(mockTask.is_generating).toBe(false);
      expect(mockTask.is_draft).toBe(true);
    });

    it('should trigger bulk generation after task creation', async () => {
      // Mock the API call that should be made after task creation
      const expectedApiCall = {
        method: 'POST',
        url: '/api/content-generation/trigger',
        body: {
          action: 'bulk_campaign_generation',
          campaign_id: 'test-campaign-id',
          company_id: 'test-company-id',
          priority: 'high'
        }
      };

      expect(expectedApiCall.body.action).toBe('bulk_campaign_generation');
      expect(expectedApiCall.body.campaign_id).toBe('test-campaign-id');
      expect(expectedApiCall.body.company_id).toBe('test-company-id');
    });
  });

  describe('Error Handling', () => {
    it('should handle OpenRouter API failures gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('OpenRouter API Error'));

      try {
        await fetch('/api/ai/stream-content', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.AI_API_TOKEN}`
          },
          body: JSON.stringify({
            messages: [{ role: 'user', content: 'test prompt' }],
            model: 'openai/gpt-4.1',
            temperature: 1,
            stream: false
          })
        });
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('OpenRouter API Error');
      }
    });

    it('should handle database update failures', () => {
      // Mock database update failure
      const mockError = new Error('Database connection failed');
      
      // This would be caught and handled by the content generation service
      expect(mockError.message).toBe('Database connection failed');
    });
  });
});

// Helper function to build test prompts (simplified version of the actual logic)
function buildTestPrompt(data: any, contextData: any): string {
  return `
    <CONTEXT_BLOCK>
      <CONTENT_BRIEF>
        <Channel>${data.channel || 'Not specified'}</Channel>
        <ContentType>${data.contentType || 'Not specified'}</ContentType>
        <Topic>${data.taskDescription}</Topic>
      </CONTENT_BRIEF>
      <AUDIENCE_CONTEXT></AUDIENCE_CONTEXT>
      <RESEARCH_MATERIALS></RESEARCH_MATERIALS>
      <COMPANY_PRODUCT_KNOWLEDGE_BASE></COMPANY_PRODUCT_KNOWLEDGE_BASE>
      <KEYWORD_STRATEGY></KEYWORD_STRATEGY>
      <BRAND_GUIDELINES>${JSON.stringify(contextData.companyBrand)}</BRAND_GUIDELINES>
    </CONTEXT_BLOCK>
  `;
}
