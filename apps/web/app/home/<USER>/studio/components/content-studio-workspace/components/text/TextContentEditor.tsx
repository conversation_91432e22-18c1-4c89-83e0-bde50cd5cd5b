'use client'
import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief } from '~/utils/brief.util';
import { ScrollArea } from "@kit/ui/scroll-area";
import { Badge } from "@kit/ui/badge";
import { Label } from "@kit/ui/label";
import { Separator } from "@kit/ui/separator";
import { Textarea } from "@kit/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kit/ui/tabs";
import { updateCompanyContent } from '~/services/company-content';
import { Loader2 } from "lucide-react";
import { useBaseContent, useEditorContent } from '../../context/ContentStudioContext';
import { SelectedDocument } from '~/components/document-selector';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@kit/ui/accordion";
import { Persona } from '~/types/persona';
import { AdvancedOptions } from './AdvancedOptions';
import { getAIExtension } from "@blocknote/xl-ai";
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useParams, usePathname } from 'next/navigation';
import { useContentGeneration } from '../../../../../../../hooks/use-content-generation';

// import { useRouter } from 'next/navigation';
interface GeneratedContent {
  content: string;
  cta_variations: string[];
  headline: string;
  rationale_for_creative_choices: string;
  seo_keywords_used: string[];
  trend_keywords_used: string[];
}

export const TextContentEditor: React.FC = () => {
  // Get data from context
  const params = useParams();
  const contentId = params.id;
    const { editor } = useEditorContent();
    const zero = useZero();
    const workspace = useTeamAccountWorkspace();
    const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];
  
    const [savedResearch] = useZeroQuery(
    zero.query.saved_research
    .where("account_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );
   
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );

  const [personas] = useZeroQuery(
    zero.query.personas
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );

  const [icps] = useZeroQuery(
    zero.query.icps
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );

  // Add company_campaigns query
  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );

  // Add product_documents query
  const [productDocuments] = useZeroQuery(
    zero.query.product_documents
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1m'
    }
  );

  // Local state

  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  // Use the content generation hook
  const { generateContent: generateContentHook, generateContentStreaming: generateContentStreamingHook, isGenerating, error } = useContentGeneration();
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  
  // Advanced options state
  const [trendKeywords, setTrendKeywords] = useState<string[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocument[]>([]);
  const [selectedIcps, setSelectedIcps] = useState<string[]>([]);
  const [icpItems, setIcpItems] = useState<any[]>(icps || []);
  const [selectedPersonas, setSelectedPersonas] = useState<any[]>([]);
  const [personaItems, setPersonaItems] = useState<any[]>(personas || []);
  const [selectedResearch, setSelectedResearch] = useState<string[]>([]);
  const [researchItems, setResearchItems] = useState<any[]>(savedResearch || []);
  console.log('researchItems', selectedResearch);
  // Initialize task title and description from company content
  useEffect(() => {
    if (selectedCompanyContent) {
      setTaskTitle(selectedCompanyContent?.task_title || '');
      setTaskDescription(selectedCompanyContent?.task_description || '');
    }
  }, [selectedCompanyContent]);

  // Pre-populate advanced options from campaign data
  useEffect(() => {

    if (selectedCompanyContent?.campaign_id && companyCampaigns?.length > 0) {
      const associatedCampaign = companyCampaigns.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id
      );
      console.log('associatedCampaign', associatedCampaign);
      if (associatedCampaign) {
        // Pre-populate target_icps
        if (associatedCampaign.target_icps && Array.isArray(associatedCampaign.target_icps)) {
          setSelectedIcps(associatedCampaign.target_icps);
        }
        
        // Pre-populate target_personas
        if (associatedCampaign.target_personas && Array.isArray(associatedCampaign.target_personas)) {
          setSelectedPersonas(associatedCampaign.target_personas);
        }
        
        // Pre-populate external_research
        if (associatedCampaign.external_research && Array.isArray(associatedCampaign.external_research)) {
          setSelectedResearch(associatedCampaign.external_research);
        }
       
        // Pre-populate documents - convert document IDs to SelectedDocument objects
        if (associatedCampaign.documents && Array.isArray(associatedCampaign.documents) && productDocuments?.length > 0) {
          const selectedDocs: SelectedDocument[] = associatedCampaign.documents
            .map((docId: string) => {
              const doc = productDocuments.find((pd: any) => pd.id === docId);
              return doc ? {
                id: doc.id,
                documentTitle: doc.title,
                content: doc.content || ''
              } : null;
            })
            .filter(Boolean) as SelectedDocument[];
          
          setSelectedDocuments(selectedDocs);
        }
      }
    }
  }, [selectedCompanyContent, companyCampaigns, productDocuments]);


  const handleGenerateContent = async () => {
    console.log("Generating content with streaming");

    if (!selectedCompanyContent?.id || !editor) return;

    try {
      const options = {
        taskTitle,
        taskDescription,
        selectedPersonas,
        selectedIcps,
        selectedResearch,
        selectedDocuments,
        seoKeywords,
        trendKeywords,
        companyContentId: selectedCompanyContent.id,
        channel: selectedCompanyContent.channel || undefined,
        contentType: selectedCompanyContent.content_type || undefined
      };

      // Use streaming generation for manual generation (shows live progress)
      const result = await generateContentStreamingHook(options, editor);

      if (!result.success) {
        console.error("Content generation failed:", result.error);
      }
    } catch (error) {
      console.error("Error generating content:", error);
    }
  }

  const onDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setTaskDescription(e.target.value);
      zero.mutate.company_content.update({
        id: selectedCompanyContent?.id || '',
        values: {
          task_description: e.target.value,
        }
      });
  }

  return (
    <div className="space-y-4 p-4">
      {/* <div className="space-y-2">
        <Label className="text-lg font-semibold">Task Title</Label>
        <Textarea 
          value={taskTitle} 
          onChange={(e) => setTaskTitle(e.target.value)}
          className="text-lg"
          rows={2}
          disabled={isGenerating}
        />
      </div> */}

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-sm text-muted-foreground">Enter the topic or basis used to generate the content.</Label>
            
            <Textarea 
              value={taskDescription} 
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-sm text-muted-foreground">Enter the topic or basis used to generate the content.</Label>
            <Textarea 
              value={taskDescription}   
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
          <AdvancedOptions
            selectedDocuments={selectedDocuments}
            onDocumentsChange={setSelectedDocuments}
            selectedIcps={selectedIcps}
            onIcpsChange={setSelectedIcps}
            icps={icpItems}
            onIcpsListChange={setIcpItems}
            selectedPersonas={selectedPersonas}
            onPersonasChange={setSelectedPersonas}
            personas={personaItems}
            onPersonasListChange={setPersonaItems}
            trendKeywords={trendKeywords}
            onTrendKeywordsChange={setTrendKeywords}
            seoKeywords={seoKeywords}
            onSeoKeywordsChange={setSeoKeywords}
            selectedResearch={selectedResearch}
            onResearchChange={setSelectedResearch}
            researchItems={researchItems}
            onResearchItemsChange={setResearchItems}
          />
        </TabsContent>
      </Tabs>
      
      <Button
        onClick={handleGenerateContent}
        disabled={isGenerating || !taskTitle.trim() || !taskDescription.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          'Generate'
        )}
      </Button>

      {error && (
        <div className="mt-2">
          <button
            onClick={handleGenerateContent}
            className="text-sm text-red-600 hover:text-red-800 underline cursor-pointer"
          >
            Error Generating, Click to try again
          </button>
        </div>
      )}

      {/* {generatedContent && (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="keywords">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Keywords</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">SEO Keywords</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {generatedContent.seo_keywords_used.map((keyword, index) => (
                          <Badge key={index} variant="secondary">{keyword}</Badge>
                        ))}
                      </div>
                    </div>
                    {generatedContent.trend_keywords_used.length > 0 && (
                      <div>
                        <Separator className="my-4" />
                        <Label className="text-sm">Trend Keywords</Label>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {generatedContent.trend_keywords_used.map((keyword, index) => (
                            <Badge key={index} variant="outline">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="rationale">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Creative Rationale</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <p>{generatedContent.rationale_for_creative_choices}</p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

          </div>
        </ScrollArea>
      )} */}
    </div>
  );
};