'use client';

import { useState, useEffect, useCallback } from 'react';
import { getSupabaseClient } from '@kit/supabase/client';
import { ProgressUpdate, ProgressSubscription } from '../services/real-time-progress-tracker';

// Hook for tracking content generation progress in React components
export function useContentGenerationProgress(campaignId: string) {
  const [progress, setProgress] = useState<ProgressUpdate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscription, setSubscription] = useState<ProgressSubscription | null>(null);

  const supabase = getSupabaseClient();

  // Initialize progress tracking
  const initializeProgress = useCallback(async (totalTasks: number, metadata?: Record<string, any>) => {
    try {
      const { getRealTimeProgressTracker } = await import('../services/real-time-progress-tracker');
      const tracker = getRealTimeProgressTracker(supabase);
      
      const initialProgress = tracker.initializeProgress(campaignId, totalTasks, undefined, metadata);
      setProgress(initialProgress);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize progress tracking';
      setError(errorMessage);
      console.error('Error initializing progress:', err);
    }
  }, [campaignId, supabase]);

  // Subscribe to progress updates
  useEffect(() => {
    let mounted = true;

    const subscribeToProgress = async () => {
      try {
        setIsLoading(true);
        
        const { getRealTimeProgressTracker } = await import('../services/real-time-progress-tracker');
        const tracker = getRealTimeProgressTracker(supabase);

        // Get current progress
        const currentProgress = tracker.getProgress(campaignId);
        if (currentProgress && mounted) {
          setProgress(currentProgress);
        }

        // Subscribe to updates
        const sub = tracker.subscribe(campaignId, (update: ProgressUpdate) => {
          if (mounted) {
            setProgress(update);
            setError(null);
          }
        });

        if (mounted) {
          setSubscription(sub);
        }

      } catch (err) {
        if (mounted) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to subscribe to progress updates';
          setError(errorMessage);
          console.error('Error subscribing to progress:', err);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    subscribeToProgress();

    return () => {
      mounted = false;
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [campaignId, supabase]);

  // Cleanup subscription on unmount
  useEffect(() => {
    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [subscription]);

  // Helper functions
  const getProgressPercentage = useCallback(() => {
    return progress?.progress || 0;
  }, [progress]);

  const getEstimatedTimeRemaining = useCallback(() => {
    if (!progress?.estimatedTimeRemaining) return null;
    
    const minutes = Math.floor(progress.estimatedTimeRemaining / 60000);
    const seconds = Math.floor((progress.estimatedTimeRemaining % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }, [progress]);

  const getThroughput = useCallback(() => {
    if (!progress?.throughput) return null;
    return `${progress.throughput.toFixed(1)} tasks/sec`;
  }, [progress]);

  const getStatusMessage = useCallback(() => {
    if (!progress) return 'Initializing...';
    
    switch (progress.status) {
      case 'pending':
        return 'Waiting to start...';
      case 'in_progress':
        return `Processing ${progress.completedTasks}/${progress.totalTasks} tasks...`;
      case 'completed':
        return `Completed! ${progress.completedTasks} tasks processed successfully.`;
      case 'failed':
        return `Failed. ${progress.completedTasks} tasks completed, ${progress.failedTasks} failed.`;
      case 'cancelled':
        return 'Cancelled by user.';
      default:
        return 'Unknown status';
    }
  }, [progress]);

  const isComplete = useCallback(() => {
    return progress?.status === 'completed' || progress?.status === 'failed' || progress?.status === 'cancelled';
  }, [progress]);

  const hasErrors = useCallback(() => {
    return progress?.errors && progress.errors.length > 0;
  }, [progress]);

  return {
    // State
    progress,
    isLoading,
    error,
    
    // Helper functions
    initializeProgress,
    getProgressPercentage,
    getEstimatedTimeRemaining,
    getThroughput,
    getStatusMessage,
    isComplete,
    hasErrors,
    
    // Raw data access
    totalTasks: progress?.totalTasks || 0,
    completedTasks: progress?.completedTasks || 0,
    failedTasks: progress?.failedTasks || 0,
    status: progress?.status || 'pending',
    errors: progress?.errors || [],
    startTime: progress?.startTime,
    lastUpdate: progress?.lastUpdate
  };
}

// Hook for managing multiple campaign progress tracking
export function useMultiCampaignProgress(campaignIds: string[]) {
  const [progressMap, setProgressMap] = useState<Map<string, ProgressUpdate>>(new Map());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const supabase = getSupabaseClient();

  useEffect(() => {
    let mounted = true;
    const subscriptions: ProgressSubscription[] = [];

    const subscribeToAllCampaigns = async () => {
      try {
        setIsLoading(true);
        
        const { getRealTimeProgressTracker } = await import('../services/real-time-progress-tracker');
        const tracker = getRealTimeProgressTracker(supabase);

        for (const campaignId of campaignIds) {
          const sub = tracker.subscribe(campaignId, (update: ProgressUpdate) => {
            if (mounted) {
              setProgressMap(prev => new Map(prev.set(campaignId, update)));
            }
          });
          subscriptions.push(sub);
        }

      } catch (err) {
        if (mounted) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to subscribe to progress updates';
          setError(errorMessage);
          console.error('Error subscribing to multi-campaign progress:', err);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    if (campaignIds.length > 0) {
      subscribeToAllCampaigns();
    } else {
      setIsLoading(false);
    }

    return () => {
      mounted = false;
      subscriptions.forEach(sub => sub.unsubscribe());
    };
  }, [campaignIds, supabase]);

  const getProgressForCampaign = useCallback((campaignId: string) => {
    return progressMap.get(campaignId) || null;
  }, [progressMap]);

  const getOverallProgress = useCallback(() => {
    const progresses = Array.from(progressMap.values());
    if (progresses.length === 0) return null;

    const totalTasks = progresses.reduce((sum, p) => sum + p.totalTasks, 0);
    const completedTasks = progresses.reduce((sum, p) => sum + p.completedTasks, 0);
    const failedTasks = progresses.reduce((sum, p) => sum + p.failedTasks, 0);

    return {
      totalTasks,
      completedTasks,
      failedTasks,
      progress: totalTasks > 0 ? Math.round(((completedTasks + failedTasks) / totalTasks) * 100) : 0,
      activeCampaigns: progresses.filter(p => p.status === 'in_progress').length,
      completedCampaigns: progresses.filter(p => p.status === 'completed').length,
      failedCampaigns: progresses.filter(p => p.status === 'failed').length
    };
  }, [progressMap]);

  return {
    progressMap,
    isLoading,
    error,
    getProgressForCampaign,
    getOverallProgress,
    campaignCount: campaignIds.length
  };
}

// Hook for triggering content generation with progress tracking
export function useContentGenerationTrigger() {
  const [isTriggering, setIsTriggering] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const triggerGeneration = useCallback(async (
    campaignId: string,
    companyId: string,
    priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'
  ) => {
    try {
      setIsTriggering(true);
      setError(null);

      const response = await fetch('/api/content-generation/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'bulk_campaign_generation',
          campaign_id: campaignId,
          company_id: companyId,
          priority,
          optimization: {
            use_queue: true,
            enhanced_processing: true
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to trigger generation: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to trigger content generation';
      setError(errorMessage);
      throw err;
    } finally {
      setIsTriggering(false);
    }
  }, []);

  return {
    triggerGeneration,
    isTriggering,
    error
  };
}
