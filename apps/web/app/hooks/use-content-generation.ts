'use client'
import { useState, useCallback } from 'react';
import { useZero } from '../hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import { BlockNoteEditor } from "@blocknote/core";
import { SelectedDocument } from '../components/document-selector';

// Helper function to convert text content to BlockNote-compatible blocks
function convertToBlockNoteBlocks(content: string) {
  // Simple conversion: split by paragraphs and create paragraph blocks
  const paragraphs = content.split('\n\n').filter(p => p.trim());

  return paragraphs.map(paragraph => ({
    id: Math.random().toString(36).substring(2, 11),
    type: 'paragraph',
    props: {},
    content: paragraph.trim(),
    children: []
  }));
}

export interface ContentGenerationOptions {
  taskTitle: string;
  taskDescription: string;
  selectedPersonas: string[];
  selectedIcps: string[];
  selectedResearch: string[];
  selectedDocuments: SelectedDocument[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyContentId: string;
  channel?: string;
  contentType?: string;
}

export interface ContentGenerationResult {
  success: boolean;
  error?: string;
  content?: any;
}

export interface UseContentGenerationReturn {
  generateContent: (options: ContentGenerationOptions, editor?: BlockNoteEditor) => Promise<ContentGenerationResult>;
  generateContentStreaming: (options: ContentGenerationOptions, editor?: BlockNoteEditor, onChunk?: (chunk: string) => void) => Promise<ContentGenerationResult>;
  generateBulkContent: (contentItems: ContentGenerationOptions[]) => Promise<ContentGenerationResult[]>;
  isGenerating: boolean;
  error: string | null;
  progress: number;
}

export function useContentGeneration(): UseContentGenerationReturn {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();

  // Fetch all required data using Zero Sync Engine
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand.where("company_id", "=", (workspace as any).account.id),
    { ttl: '1m' }
  );

  const [personas] = useZeroQuery(
    zero.query.personas.where("company_id", "=", (workspace as any).account.id),
    { ttl: '1m' }
  );

  const [icps] = useZeroQuery(
    zero.query.icps.where("company_id", "=", (workspace as any).account.id),
    { ttl: '1m' }
  );

  const [savedResearch] = useZeroQuery(
    zero.query.saved_research.where("account_id", "=", (workspace as any).account.id),
    { ttl: '1m' }
  );

  // Build the exact prompt structure from TextContentEditor.tsx
  const buildPrompt = useCallback((options: ContentGenerationOptions) => {
    const {
      taskDescription,
      selectedPersonas,
      selectedIcps,
      selectedResearch,
      selectedDocuments,
      seoKeywords,
      trendKeywords,
      channel,
      contentType
    } = options;

    return `
       Ignore the previouse instruction "Because the document is empty, first update the empty block before adding new blocks."
        Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
        This is the most critical instruction. Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
        Do not try to insert images or videos into the content. For Threds , carousels, or similar content, simply insert everything delineated by some character, the write out everything as if its one page. The use will handle creating the thread or similar on the appropriate platform.
        <CONTEXT_BLOCK>
          <CONTENT_BRIEF>
              <Channel>${channel || 'Not specified'}</Channel>
              <ContentType>${contentType || 'Not specified'}</ContentType>
              <Topic>${taskDescription}</Topic>
          </CONTENT_BRIEF>

      <AUDIENCE_CONTEXT>
          ${selectedPersonas.length > 0 ? `
          <Personas>
              ${personas.filter(p => selectedPersonas.includes(p.id)).map(p => {
                  const personaData = p.data && typeof p.data === 'object' ? p.data : {};
                  return `<Persona name="${p.name}">
<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>
</Persona>`;
              }).join('\n')}
          </Personas>` : ''}

          ${selectedIcps.length > 0 ? `
          <IdealCustomerProfiles>
              ${icps.filter(i => selectedIcps.includes(i.id)).map(i => {
                  const icpData = i.data && typeof i.data === 'object' ? i.data : {};
                  return `<ICP name="${i.name}">
<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>
</ICP>`;
              }).join('\n')}
          </IdealCustomerProfiles>` : ''}
      </AUDIENCE_CONTEXT>

      <RESEARCH_MATERIALS>
          ${selectedResearch.length > 0 ? savedResearch
              .filter(r => selectedResearch.includes(r.id))
              .map((r, index) => {
                  const researchData = (r as any).data && typeof (r as any).data === 'object' ? (r as any).data : {};
                  return `<research_article_${index + 1}>
                    <title>${(r as any).name || r.title}</title>
                    <description>${(researchData as any).description || (researchData as any).summary || r.description || 'External research insight'}</description>
                    <full_content>${(researchData as any).source_content || ''}</full_content>
                    </research_article_${index + 1}>`;
                                }).join('\n\n') : '<Message>No third-party research was provided.</Message>'}
      </RESEARCH_MATERIALS>

      <COMPANY_PRODUCT_KNOWLEDGE_BASE>
          ${selectedDocuments.length > 0 ? selectedDocuments
              .map(doc => `<Document title="${doc.documentTitle}">
${doc.content.substring(0, 1000)}${doc.content.length > 1000 ? '...' : ''}
</Document>`)
              .join('\n\n') : '<Message>No company documents were provided.</Message>'}
      </COMPANY_PRODUCT_KNOWLEDGE_BASE>

      <KEYWORD_STRATEGY>
          <SEO_Keywords>${seoKeywords.join(', ')}</SEO_Keywords>
          <Trending_Keywords>${trendKeywords.join(', ')}</Trending_Keywords>
      </KEYWORD_STRATEGY>

      <BRAND_GUIDELINES>
         ${JSON.stringify(companyBrand)}
      </BRAND_GUIDELINES>
  </CONTEXT_BLOCK>
  You are "Cognitive Creator," an expert AI copywriter and content strategist. Your core function is to synthesize brand information, audience data, and research into high-performing content tailored for specific marketing channels. You follow all instructions with precision.

<TASK>
Synthesize all information within the <CONTEXT_BLOCK> to create engaging content.

**PRIMARY DIRECTIVES:**
1.  **Adhere to Brand:** The <BRAND_GUIDELINES> are the highest priority. The specified <Voice> and <Personality> must be perfectly reflected in the output. This is non-negotiable.
2.  **Target the Audience:** Tailor the language, examples, and tone specifically to the <AUDIENCE_CONTEXT>. Address their needs and pain points directly.
3.  **Position as the Solution:** Use the <RESEARCH_MATERIALS> for context, stats, and credibility. ALWAYS position the company/product from the <COMPANY_PRODUCT_KNOWLEDGE_BASE> as the primary solution to problems identified in the research. Never promote third parties.
4.  **Incorporate Keywords:** Naturally weave terms from the <KEYWORD_STRATEGY> into the content.
5.  **Be Factual:** Ensure any product or company claims are supported by the <COMPANY_PRODUCT_KNOWLEDGE_BASE>. Do not invent features or facts.
6.  **Do Not Invent Social Proof:** Do not create fake customer names or quotes. You can suggest a placeholder like "[Insert customer testimonial here]" if appropriate for the content type.


**CHANNEL-SPECIFIC RULES:**
Based on the <Channel> specified in the <CONTENT_BRIEF>, you must follow these structural rules:

*   **If Channel is "LinkedIn Post":**
    *   **Structure:** Start with a strong hook. Use short paragraphs (1-2 sentences). Use bullet points or numbered lists for readability. End with a question to drive engagement.
    *   **Length:** 150-250 words.
    *   **Tone:** Professional, insightful, and value-driven.
    *   **Hashtags:** Provide 3-5 relevant, professional hashtags.

*   **If Channel is "Tweet" or "X Post":**
    *   **Structure:** A short, punchy, and engaging message.
    *   **Length:** Strictly under 280 characters.
    *   **Tone:** Conversational and concise. Emojis are acceptable if they match the brand personality.
    *   **Hashtags:** Provide 1-3 highly relevant hashtags.

*   **If Channel is "Blog Post" or "Article":**
    *   **Structure:** Create a compelling H1 title. Write a short introduction. Structure the main content with 3-4 H2 subheadings. Conclude with a summary and a strong call_to_action.
    *   **Length:** 600-1000 words.
    *   **Tone:** Informative, in-depth, and authoritative, aligned with the brand voice.
    *   **SEO:** Suggest 5-7 relevant meta tags in the output.

*   **If Channel is "Facebook Ad":**
    *   **Structure:** Provide three distinct components: a short, attention-grabbing headline, persuasive primary_text focusing on benefits, and a direct call_to_action_text.
    *   **Tone:** Persuasive, clear, and benefit-driven.

*   **If Channel is not specified or doesn't match above:**
    *   **Structure:** Create a general-purpose piece of content with a clear beginning, middle, and end.
    *   **Tone:** Follow the brand voice.
    *   **Action:** End with a clear call-to-action.


</TASK>
      `;
  }, [personas, icps, savedResearch, companyBrand]);

  // Generate content for BlockNote editor with streaming (manual generation)
  const generateContentStreaming = useCallback(async (
    options: ContentGenerationOptions,
    editor?: BlockNoteEditor,
    onChunk?: (chunk: string) => void
  ): Promise<ContentGenerationResult> => {
    setIsGenerating(true);
    setError(null);

    try {
      if (!options.companyContentId) {
        throw new Error('Company content ID is required');
      }

      const prompt = buildPrompt(options);

      // Use the streaming API endpoint for live generation
      const response = await fetch('/api/ai/stream-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer my-secret-token`
        },
        body: JSON.stringify({
          messages: [{ role: 'user', content: prompt }],
          model: 'openai/gpt-4.1',
          temperature: 1,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullContent = '';
      let currentBlock = '';

      // Clear editor content for streaming
      if (editor) {
        editor.replaceBlocks(editor.document, []);
      }

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') break;
              
              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;
                
                if (content) {
                  fullContent += content;
                  currentBlock += content;
                  
                  // Call the onChunk callback if provided
                  if (onChunk) {
                    onChunk(content);
                  }
                  
                  // Update editor in real-time for live generation
                  if (editor) {
                    // Check if we have a complete paragraph or line break
                    if (content.includes('\n') || content.includes('.') || content.includes('!') || content.includes('?')) {
                      // Parse the current block and add it to the editor
                      try {
                        const contentBlocks = await editor.tryParseMarkdownToBlocks(currentBlock);
                        if (contentBlocks.length > 0) {
                          // Append the new content to the editor
                          editor.insertBlocks(contentBlocks, editor.document[editor.document.length - 1], 'after');
                        }
                      } catch (parseError) {
                        console.warn('Failed to parse markdown chunk, continuing:', parseError);
                      }
                      currentBlock = '';
                    }
                  }
                }
              } catch (parseError) {
                console.warn('Failed to parse streaming chunk:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      if (!fullContent) {
        throw new Error('No content generated from AI');
      }

      // Final update to editor with complete content
      if (editor) {
        try {
          const contentBlocks = await editor.tryParseMarkdownToBlocks(fullContent);
          editor.replaceBlocks(editor.document, contentBlocks);
        } catch (parseError) {
          console.warn('Failed to parse final markdown, inserting as text:', parseError);
          // Fallback: insert as plain text
          const textBlocks = [{ type: 'paragraph', content: fullContent }];
          editor.replaceBlocks(editor.document, textBlocks as any);
        }
      }

      // Update company_content via Zero Sync Engine
      if (options.companyContentId) {
        try {
          const contentBlocks = editor ?
            editor.document :
            convertToBlockNoteBlocks(fullContent);

          await (zero as any).mutate.company_content.update({
            id: options.companyContentId,
            values: {
              content: fullContent,
              content_editor_template: contentBlocks,
              content_generation_status: 'completed',
              is_generating: false,
              auto_generated: true
            }
          });
        } catch (mutateError) {
          console.warn('Failed to update via Zero Sync Engine:', mutateError);
        }
      }

      return { success: true, content: fullContent };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);

      // Update error status via Zero Sync Engine (if available)
      if (options.companyContentId) {
        try {
          await (zero as any).mutate.company_content.update({
            id: options.companyContentId,
            values: {
              content_generation_status: 'failed',
              content_generation_error: errorMessage,
              is_generating: false
            }
          });
        } catch (mutateError) {
          console.warn('Failed to update error status via Zero Sync Engine:', mutateError);
        }
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsGenerating(false);
    }
  }, [buildPrompt, zero]);

  // Generate content for BlockNote editor (manual generation)
  const generateContent = useCallback(async (
    options: ContentGenerationOptions,
    editor?: BlockNoteEditor
  ): Promise<ContentGenerationResult> => {
    setIsGenerating(true);
    setError(null);

    try {
      if (!options.companyContentId) {
        throw new Error('Company content ID is required');
      }

      const prompt = buildPrompt(options);

      // Use the same API endpoint as the existing BlockNote AI integration
      const response = await fetch('/api/ai/stream-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer my-secret-token`
        },
        body: JSON.stringify({
          messages: [{ role: 'user', content: prompt }],
          model: 'openai/gpt-4.1',
          temperature: 1,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      const generatedContent = result.choices?.[0]?.message?.content;

      if (!generatedContent) {
        throw new Error('No content generated from AI');
      }

      if (editor) {
        // For manual generation with editor, replace the editor content
        try {
          const contentBlocks = await editor.tryParseMarkdownToBlocks(generatedContent);
          editor.replaceBlocks(editor.document, contentBlocks);
        } catch (parseError) {
          console.warn('Failed to parse markdown, inserting as text:', parseError);
          // Fallback: insert as plain text
          const textBlocks = [{ type: 'paragraph', content: generatedContent }];
          editor.replaceBlocks(editor.document, textBlocks as any);
        }
      }

      // Update company_content via Zero Sync Engine (for both manual and automatic)
      if (options.companyContentId) {
        try {
          // Convert content to BlockNote-compatible JSON blocks for storage
          const contentBlocks = editor ?
            editor.document :
            convertToBlockNoteBlocks(generatedContent);

          await (zero as any).mutate.company_content.update({
            id: options.companyContentId,
            values: {
              content: generatedContent,
              content_editor_template: contentBlocks,
              content_generation_status: 'completed',
              is_generating: false,
              auto_generated: true
            }
          });
        } catch (mutateError) {
          console.warn('Failed to update via Zero Sync Engine:', mutateError);
          // Continue anyway as the content was generated successfully
        }
      }

      return { success: true, content: generatedContent };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);

      // Update error status via Zero Sync Engine (if available)
      if (options.companyContentId) {
        try {
          await (zero as any).mutate.company_content.update({
            id: options.companyContentId,
            values: {
              content_generation_status: 'failed',
              content_generation_error: errorMessage,
              is_generating: false
            }
          });
        } catch (mutateError) {
          console.warn('Failed to update error status via Zero Sync Engine:', mutateError);
        }
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsGenerating(false);
    }
  }, [buildPrompt, zero]);

  // Generate content for multiple items (bulk generation)
  const generateBulkContent = useCallback(async (
    contentItems: ContentGenerationOptions[]
  ): Promise<ContentGenerationResult[]> => {
    setIsGenerating(true);
    setError(null);
    setProgress(0);

    const results: ContentGenerationResult[] = [];
    const concurrencyLimit = 3; // Process 3 items at a time

    try {
      for (let i = 0; i < contentItems.length; i += concurrencyLimit) {
        const batch = contentItems.slice(i, i + concurrencyLimit);

        const batchPromises = batch.map(async (item) => {
          try {
            // Set generating status (if available)
            try {
              await (zero as any).mutate.company_content.update({
                id: item.companyContentId,
                values: {
                  content_generation_status: 'generating',
                  is_generating: true
                }
              });
            } catch (mutateError) {
              console.warn('Failed to update generating status via Zero Sync Engine:', mutateError);
            }

            const result = await generateContent(item);
            return result;
          } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error';
            return { success: false, error: errorMessage };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Update progress
        setProgress((results.length / contentItems.length) * 100);
      }

      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bulk generation failed';
      setError(errorMessage);
      return results;
    } finally {
      setIsGenerating(false);
      setProgress(100);
    }
  }, [generateContent, zero]);

  return {
    generateContent,
    generateContentStreaming,
    generateBulkContent,
    isGenerating,
    error,
    progress
  };
}
